// vite.config.ts
import { loadEnv } from "file:///Users/<USER>/inngke/other/cloudShopAI-PC/node_modules/vite/dist/node/index.js";
import vue from "file:///Users/<USER>/inngke/other/cloudShopAI-PC/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import legacy from "file:///Users/<USER>/inngke/other/cloudShopAI-PC/node_modules/@vitejs/plugin-legacy/dist/index.mjs";
import vueJsx from "file:///Users/<USER>/inngke/other/cloudShopAI-PC/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import path from "path";
import VueSetupExtend from "file:///Users/<USER>/inngke/other/cloudShopAI-PC/node_modules/vite-plugin-vue-setup-extend/dist/index.mjs";
import postcssPresetEnv from "file:///Users/<USER>/inngke/other/cloudShopAI-PC/node_modules/postcss-preset-env/dist/index.mjs";
var __vite_injected_original_dirname = "/Users/<USER>/inngke/other/cloudShopAI-PC";
var vite_config_default = ({ mode }) => {
  let envPrefix = "VUE_";
  const root = process.cwd();
  const env = loadEnv(mode, root, envPrefix);
  const defineList = Object.entries(env).filter(([key]) => key.startsWith(envPrefix)).map(([key, val]) => ({ [`process.env.${key}`]: JSON.stringify(val ?? "") }));
  return {
    base: env.VUE_APP_PUBLIC_PATH,
    // 兼容 Cli, 合并环境变量
    define: Object.assign({}, ...defineList),
    // 统一webpack的环境变量前缀
    envPrefix,
    plugins: [
      legacy({
        targets: ["defaults", "not IE 11"]
      }),
      vue(),
      vueJsx(),
      VueSetupExtend()
      // AutoImport({
      //     dts: "src/types/auto-imports.d.ts",
      //     resolvers: [TDesignResolver({ library: "vue-next" })],
      // }),
      // Components({
      //     dirs: [],
      //     dts: "src/types/auto-global-gomponents.d.ts",
      //     resolvers: [TDesignResolver({ library: "vue-next" })],
      // }),
    ],
    resolve: {
      alias: {
        "~@": path.join(__vite_injected_original_dirname, "./src"),
        "@": path.join(__vite_injected_original_dirname, "./src"),
        "#": path.join(__vite_injected_original_dirname, "./src/types"),
        "~": path.join(__vite_injected_original_dirname, "./src/assets")
      }
    },
    server: {
      host: true,
      port: 80,
      proxy: {
        [`^${env.VUE_APP_PROXY_KEY}`]: {
          target: env.VUE_APP_PROXY_URL_API,
          ws: false,
          changeOrigin: true,
          rewrite: (_) => _.replace(new RegExp(`^${env.VUE_APP_PROXY_KEY}`), "")
        }
      }
    },
    build: {
      sourcemap: mode === "test",
      minify: "terser",
      terserOptions: {
        compress: {
          // 生产环境去掉console
          drop_console: mode === "production",
          drop_debugger: mode === "production"
        }
      }
    },
    css: {
      preprocessorOptions: {
        less: {
          additionalData: `@import "@/assets/styles/variables.less";`,
          javascriptEnabled: true
        }
      },
      postcss: {
        plugins: [
          postcssPresetEnv({
            stage: 2,
            autoprefixer: {
              add: true,
              remove: true
            },
            browsers: ["Chrome >= 49", "Firefox >= 52", "Safari >= 10", "Edge >= 79", "> 1%", "not dead"]
          })
        ]
      }
    }
  };
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
