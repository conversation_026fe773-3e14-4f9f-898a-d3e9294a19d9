<template>
    <Cascader checkStrictly :placeholder="placeholder" :options="folderTree || folderOptions" :value="value" :disabled="disabled" :keys="keys" @change="handleSearch" />
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { Cascader } from "tdesign-vue-next";

import { Folder } from "#/generate";
import { treeToMap } from "@/utils/fn";
import { getPathText } from "./common";
import { getFolderList } from "@/api/folder";

const props = withDefaults(
    defineProps<{
        disabled?: boolean;
        keys?: { label: string; value: string };
        placeholder?: string;
        folderTree?: Folder[];
        value: string;
    }>(),
    {
        keys: () => ({ label: "name", value: "id" }),
        placeholder: "请选择目录",
    },
);

const emit = defineEmits<{
    (e: "update:value", value: string): void;
    (e: "change", value: { value: string; content: string }): void;
}>();

// // 选中的值
// const modelValue = defineModel<string>("value");

// 目录数据
const folderOptions = ref<Folder[]>([]);
const folderMap = computed(() => treeToMap(folderOptions.value, { parentIdKey: "pid" }));

if (!props.folderTree) {
    // 获取目录数据
    getFolderList().then((res) => (folderOptions.value = res));
}

const handleSearch = (value: string | any) => {
    emit("update:value", value);
    emit("change", { value, content: getPathText(value, folderMap.value) });
};
</script>

<style scoped lang="less"></style>
