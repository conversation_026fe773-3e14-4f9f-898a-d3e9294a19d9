// 草稿类型：1=裂变混剪 2=分镜脚本 3=成片混剪 4=音乐踩点
export type ProjectType = 1 | 2 | 3 | 4;

// 素材标签
export interface Tag {
    value: string;
    title: string;
    pid?: string;
    children?: Tag[];
    // 空镜数量
    normalTotal: number;
}

// 视频素材类型
export interface VideoMaterial {
    // 素材ID（本地库）
    id?: string;
    // 素材ID，本地上传时没有值
    materialId: string;
    // 素材类型: crop_material=素材库 user_material=用户素材库
    materialType: "crop_material" | "user_material";
    // 视频时长，单位：毫秒
    duration: number;
    // 视频地址
    url: string;
    // 标签（即素材库名称）
    tags?: string[];
    // 匹配得分
    score: number;
    // 裁剪开始时间，单位：毫秒
    clipStart: number;
    // 裁剪视频时长，单位：毫秒
    clipDuration: number;
    // 分类列表
    categoryList: { id: string; name: string }[];
    // 视频速度
    speed: number;
    // 创建时间(毫秒)
    createTime: number;
    // 状态，0=处理中, 1=处理完成
    status: 0 | 1;
    // 是否播放原素材声音，默认 false
    originAudio: boolean;
    // 宽度
    width: number;
    // 高度
    height: number;
    // 旋转角度
    rotate: number;
}

// 字幕脚本
export interface Subtitle {
    // 字幕内容
    text: string;
    // 分镜序号，从0开始（界面上展示需要+1）
    scriptIndex: number;
    // 特效
    effects?: SubtitleEffect[];
    // 是否为行内样式，false为整行的
    inline: boolean | null;
    // 数字人角色下标，使用的是digitalHumanConfigs的index
    role: number;
    // 字幕打标
    wordAta: {
        // 字幕内容
        word: string;
        // 状态：-1=弃用 0=隐藏 1=正常 2=高亮
        status: -1 | 0 | 1 | 2;
    }[];
}

// 表单参数
export interface PromptMap {
    // diff应用ID
    appId: number;

    // 视频类型: 1=文案成片 2=素材成片 3=单人口播片 4=多人情景 5=音乐踩点
    videoType: 1 | 2 | 3 | 4 | 5;

    // 上传的实拍视频
    originalVideo: VideoMaterial[];
    // 上传的口播视频
    oralVideoMaterial: VideoMaterial[];
    // 空镜素材列表
    categoryIds: Pick<Tag, "value" | "title" | "pid">[];
    // 脚本
    asides: string;
    // 字体
    subtitleFontName: string;
    // 字幕样式
    subtitleTextStyle: string;
    // 字体大小
    subtitleFontSize: number;
    // 字幕位置Y轴偏移
    subtitlePositionY: number;
    // 是否开启数字人
    digitalPersonOpen: boolean;
    // 数字人展示形式: 1=浮屏+全屏 2=全屏混剪 3=始终浮屏 4=全屏数字人
    digitalPersonSwitch: 1 | 2 | 3 | 4;
    // 口播展示形式: 1=浮屏+全屏 2=全屏混剪 3=始终浮屏 4=全屏口播
    sceneVideoDisplay: 1 | 2 | 3 | 4;
    // 是否开启配音
    audioOpen: boolean;
    // 是否使用背景音乐
    bgmOpen: boolean;
    // 背景音乐类型
    bgmType: string;
    // 背景音乐ID
    chooseMusic: string;
    // 背景音乐ID列表（裂变专属）
    chooseMusicIds: string[];
    // 背景音乐音量
    bgmVolume: number;
    // 视频规格（素材规格）： 1=竖屏 2=横屏 3=不指定
    vertical: 1 | 2 | 3;
    // 画面匹配算法
    difyWeave: boolean;
    // 使用素材原声
    originAudio: boolean;
    // LUT设置
    videoLut: string;
    // 是否关闭字幕
    subtitleOff: boolean;
    // 字幕打标风格
    subtitleTagStyle: string;
    // 字幕划重点版本
    subtitleHighlightVersion: number;
    // 音频文件
    audioFile: string;
    // 音频文件名称
    audioFileName: string;
    // 分镜智能转场
    transitionType: 0 | 1;
}

// 视频素材脚本数据类型
export interface VideoMaterialScript {
    // 开始时间，格式：07:10 表示：7分10秒
    start: string;
    // 结束时间，格式：07:10 表示：7分10秒
    end: string;
    // 视频时长，单位：毫秒
    duration: number;
    // 素材ID
    materialId: string;
    // 场景、画面
    scene: string;
    // 字幕
    aside: string;
    // 已选空镜素材列表
    materialList: VideoMaterial[];
    // 备选空镜素材列表
    materialOriginList: VideoMaterial[];
    // 已选口播素材列表
    sceneMaterialList: VideoMaterial[];
    // 备选口播素材列表
    sceneMaterialOriginList: VideoMaterial[];
    // 使用的数字人: 状态 -1：不启用 1:浮屏 2:全屏
    digitalPersonDisplay: -1 | 1 | 2;
    // 贴片
    widgets: WidgetData[] | null;
    // 入场特效ID
    transitionId: number;
}

// 表单数据
export interface FormParams {
    // 视频ID
    taskId?: string;
    // 表单草稿ID
    draftId?: string;
    // 失败的重试ID
    retryTaskId?: string;
    // 任务ID
    creationTaskId?: string;
    // 视频分类ID
    videoCategoryId: string;

    // 表单数据
    promptMap: PromptMap;
    // 脚本
    scripts: VideoMaterialScript[];
    // 字幕
    subtitles: Subtitle[];
    // 数字人列表
    digitalHumanConfigs: DigitalHuman[];
    // 数字人列表(裂变专属)
    chooseDigitalHumanConfigs: DigitalHuman[];
    // 角色
    roles: string[];

    // 前贴脚本
    beforeScript: VideoMaterialScript;
    // 后贴脚本
    afterScript: VideoMaterialScript;
}

// 脚本目录
export interface Category {
    // 类型
    type: "script" | "video";
    // 目录id
    id: string;
    // 目录名称
    name: string;
    // 父级目录id
    parentId: string;
    // 排序
    sort?: number;
    // 子目录
    children?: Category[];
}

interface Folder {
    id: string;
    name: string;
    pid?: string;
    children?: Folder[];
}

// 视频成品
export interface OutputVideo {
    // 目录ID
    cateId: string;
    // id
    id: string;
    // 视频关键字
    videoKeys: string;
    // 视频标题
    title: string;
    // 视频链接
    videoUrl: string;
    // 1080P视频链接
    video1080Url: string;
    // 视频图片
    image: string;
    // 视频宽度
    width: number;
    // 视频高度
    height: number;
    // 旋转角度
    rotate: number;
    // 集合个数
    videoNum: number;
    // 总共要生成的视频数量
    videoTotal: number;
    // 状态 -2=生成失败 -1=取消生成 1=处理中 2=生成成功
    status: -2 | -1 | 1 | 2;
    // 发布状态 -1:发布失败 0:未发布 1:发布中 2:发布成功
    releaseStat: -1 | 0 | 1 | 2;
    // 是否有关联发布任务
    hasPublishTask: boolean;
    // 是否已下载
    downloaded: boolean;
    // 创建时间
    createTime: number;
    // 类型
    typeText: string;
    // 浏览量
    views: number;
    // 错误码：72300=数字人错误
    errorCode: 72300;
    // 创作失败原因
    errorMsg: string;
    // 剪映工程地址
    projectZipUrl?: string;
    // 是否可以重新创作
    recreate: boolean;
    // 创作时选择的素材库名称
    materialCategoryNames: string[];
    // 时长
    duration: number;
    // 视频地址
    videoUrl: string;
    // 广告诊断信息
    oeDiagnosis: {
        // 检测状态：-2=检测任务失败 -1=提交检测失败 0=未检测 1=已提交检测 2=检测完成
        status: -2 | -1 | 0 | 1 | 2;
        // 是否AD优质素材
        isAdHighQualityMaterial: boolean;
        // 是否千川优质素材
        isEcpHighQualityMaterial: boolean;
        // 是否首发
        isFirstPublishMaterial: boolean;
        // 是否低效
        isInefficientMaterial: boolean;
        // 非AD优质素材的原因
        notAdHighQualityReason: string;
        // 非千川优质素材的原因
        notEcpHighQualityReason: string;
        // 审核状态: 3=预审结果超时 -2=预审不通过 -1=提交失败 0=未提交 1=已提交 2=预审通过
        auditStatus: -3 | -2 | -1 | 0 | 1 | 2;
        // 审核拒绝原因
        auditRejectReason: string;
    };

    // 视频草稿类型: 1=裂变混剪, 2=分镜脚本, 3=成片混剪, 4=音乐踩点
    draftType: ProjectType;

    // 内部使用
    checked?: boolean;
}

// 视频成品详情
export interface OutputVideoDetail {
    // 视频详情
    id: string;
    // 创建时间
    createTime: number;

    // 视频列表
    videoItems: {
        id: string;
        videoUrl: string;
    }[];

    // 创建时表单参数
    request: FormParams;
}

// 编辑脚本视频props
export interface UseCurrentScriptVideoProps {
    // 字幕脚本
    subtitles: Subtitle[];
    // 数字人列表
    digitalHumanConfigs?: DigitalHuman[];
    // 数字人列表(裂变专属)
    chooseDigitalHumanConfigs?: DigitalHuman[];
    // 是否为实拍视频
    isOriginalVideo?: boolean;
    // 是否为口播视频
    isSpeechVideo?: boolean;
    // 表单数据
    formData: FormParams;
    // 是否为裂变混剪
    isFission?: boolean;
    // 是否为音乐踩点
    isRhythm?: boolean;
}

// 编辑脚本视频props
export interface VideoOperationsProps
    extends Pick<VideoMaterialScript, "duration" | "scene" | "materialList" | "materialOriginList" | "digitalPersonDisplay" | "sceneMaterialList" | "sceneMaterialOriginList" | "transitionId"> {
    // 表单id
    draftId: string;
    // 脚本索引(分镜号)
    scriptIndex: number;
    // 列索引, -1 表示添加素材
    colIndex: number;

    // 开始时间
    sceneStart: VideoMaterialScript["start"];
    // 结束时间
    sceneEnd: VideoMaterialScript["end"];
    // 禁止选择的素材列表
    forbidMaterialList?: VideoMaterial[];

    // 是否有实拍视频
    isOriginalVideo?: boolean;
    // 实拍视频列表
    originalVideo: VideoMaterial[];
    // 数字人列表
    digitalHumanConfigs: DigitalHuman[];
    // 当前镜头的字幕脚本
    subtitles: Subtitle[];
    // 是否开启数字人
    digitalPersonOpen?: boolean;
    // 空镜素材分类列表
    categoryIds: PromptMap["categoryIds"];
    // 视频规格
    vertical: PromptMap["vertical"];

    // 是否是口播视频
    isSpeechVideo?: boolean;

    // 是否为前后贴
    isPatch?: boolean;
    // 前后贴视频
    patchVideos?: VideoMaterial[];

    // 是否为裂变混剪
    isFission?: boolean;

    // 贴片
    widget: WidgetData;

    // 是否显示转场
    showTransition: boolean;

    // 需要排除的id 去重使用
    excludedIds?: string[];
}

// 修改脚本分镜事件参数
export type VideoOperationsResult = Rquired<Pick<VideoOperationsProps, "duration" | "digitalPersonDisplay" | "materialList" | "materialOriginList" | "sceneMaterialList" | "transitionId" | "patchVideos" | "widget">>;

// 贴片
export interface WidgetComponent {
    type: "text" | "image";
    // 中心坐标，X轴。0点为视频中心，上为正，下为负，1080x1920的视频取值范围为[-1920, 1920]
    x: number;
    // 中心坐标，Y轴。0点为视频中心，右为正，左为负，1080x1920的视频取值范围为[-1080, 1080]
    y: number;
    // 缩放比例，100表示不缩放，50表示缩放 50%
    zoom: number;
    // 旋转角度，0表示不旋转
    rotate: number;
    // 透明度，100表示不透明，0表示全透明，取值范围[0, 100]
    opacity: number;
    // 层级，越大越上层（下层会被下层遮挡）
    layoutIndex: number;
    // 开始展示时间，单位：毫秒，0表示直接展示
    start?: number;
    // 展示时长，单位：毫秒，-1或者null表示从start开始一直展示
    duration?: number;
    // 动画特效ID，即剪映动画的ID
    animationId: string;
}

// 图片贴片
export interface ImageWidget extends WidgetComponent {
    // 类型：image
    type: "image";
    // 图片地址
    url: string;
}

// 文本贴片
export interface TextWidgetStyle {
    // 字体大小
    fontSize: number;
    // 字体名称
    fontName: string;
    // 填充颜色
    fillColor: string;
    // 字体加粗，默认false
    bold: boolean;
    // 字间距
    textWordWidth: number;
    // 行间距
    textLineWidth: number;
    // 描边颜色
    strokeColor: string;
    // 描边宽度
    strokeWidth: number;
    // 背景颜色
    backgroundColor: string;
    // 文本对齐 0=左对齐 1=水平居中 2=右对齐 3=顶对齐 4=底对齐 5=水平分布 6=垂直分布
    textAlign: 0 | 1 | 2 | 3 | 4 | 5 | 6;
    // 背景透明度，100表示不透明，0表示全透明，取值范围[0, 100]
    backgroundOpacity: number;
    // 背景圆角，取值范围[0, 100]，单位：百分比
    backgroundRadius: number;
    // 背景高度，取值范围[0, 100]，单位：百分比
    backgroundHeight: number;
    // 背景宽度，取值范围[0, 100]，单位：百分比
    backgroundWidth: number;
    // 阴影颜色
    shadowColor: string;
    // 阴影透明度，100表示不透明，0表示全透明，取值范围[0, 100]
    shadowOpacity: number;
    // 阴影模糊度，100表示不透明，0表示全透明，取值范围[0, 100]
    shadowVague: number;
    // 阴影距离，100表示不透明，0表示全透明，取值范围[0, 100]
    shadowWidth: number;
    // 阴影角度，取值范围[-180, 180]
    shadowAngle: number;

    // 左右偏移，取值范围[0, 100]，单位：百分比
    backgroundOffsetX?: number;
    // 上下偏移，取值范围[0, 100]，单位：百分比
    backgroundOffsetY?: number;
    // 斜体，默认false
    italic?: boolean;
    // 下划线，默认false
    underline?: boolean;

    // 发光颜色
    shineColor?: string;
    // 发光强度，取值范围[10, 100]
    shineStrength?: number;
    // 发光范围，取值范围[10, 100]
    shineWidth?: number;
    // 发光水平角度，取值范围[-50, 50]
    shineAngleHorizon?: number;
    // 发光垂直角度，取值范围[-50, 50]
    shineAngleVertical?: number;
    // 弯曲程度，取值范围[-360, 360]
    bend?: number;
}
export interface TextWidget extends WidgetComponent {
    // 类型：text
    type: "text";
    // 文本内容
    text: string;
    // 全局样式
    globalStyle: TextWidgetStyle;
    // 局部文字样式
    words: (TextWidgetStyle & {
        // 文本起始位置
        index: number;
        // 文本长度
        length: number;
    })[];
}

// 贴片组件
export type Widget = TextWidget | ImageWidget;

// 贴片数据
export type WidgetData = { id?: string; widgets: Widget[] };

// 贴片配置
export type WidgetConfig<T = any> = {
    // 选项的标题
    title: string;
    // 选项的值
    value: T;
    // 图标地址
    icon: string;
    // 链接
    url: string;
};

// 贴片配置数据
export interface WidgetConfigResponse {
    // 模板列表
    widgetTemplates: (WidgetConfig & { userId?: string })[];
    // 文字样式列表
    textTemplates: WidgetConfig<TextWidget>[];
    // 贴纸列表
    stickers: WidgetConfig<{ url: string; zoom: number }>[];
    // 动画列表
    animations: WidgetConfig<WidgetComponent>[];

    // 字体列表
    fonts: WidgetConfig[];
}
