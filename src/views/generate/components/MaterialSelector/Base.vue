<template>

    <div class="material-selector-base">
        <SelectFolder
            title="素材目录"
            editable
            v-model:value="filterParams.folderIds"
            :folder-tree="folderTree"
            :enabled-ids="selectedFolderIds"
            @select="handleSearch"
        />
        <div class="material-container" ref="scrollContainer">
            <SearchInput v-if="allowSearch" class="search" v-model="keyword" :storageKey="storageKey"
                         :placeholder="placeholder" @change="handleSearch" />

            <div class="filter-section" v-if="!['speech', 'local'].includes(type)">
                <t-select v-model="filterParams.vertical" placeholder="全部规格" :options="verticalOptions" clearable
                          @change="handleSearch" />
                <t-date-range-picker allow-input clearable v-model="filterParams.__createTime__"
                                     @change="handleSearch" />
            </div>

            <div class="material-container-wrapper" >
                <div class="material-container">
                    <div class="material-item upload-btn" v-if="type === 'local'" @click="handleUpload">
                        <AddIcon />
                    </div>

                    <div class="material-item" v-for="(item, index) in materialList"
                         :key="item.materialId + item.id + index">
                        <div class="upload-progress-item" v-if="item.uploadProgress !== undefined">
                            <t-loading />
                            <div class="upload-progress">上传中 {{ item.uploadProgress }}%</div>
                        </div>

                        <template v-else>
                            <TVideo
                                :delay="[1000, 150]"
                                :src="item.url"
                                :videoWidth="item.width"
                                :videoHeight="item.height"
                                :rotate="item.rotate"
                                v-bind="getMaterialStartTimeAndEndTime(item)"
                                :duration="(type === 'speech' ? item.clipDuration : item.duration) / 1000"
                                @click="emit('select', item, keyword)"
                            />

                            <div v-if="item.status === 0" class="flag">画面处理中</div>

                            <t-checkbox :checked="selectedMap.has(getMaterialSelectId(item, type === 'speech'))"
                                        v-if="selecteds" />

                            <t-typography-paragraph ellipsis class="material-tags">{{ item.folderName }}
                            </t-typography-paragraph>

                            <div class="material-time">
                                {{ getDate(item.createTime, "YYYY-MM-DD hh:mm:ss") }}
                                <t-dropdown
                                    class="more"
                                    :options="[
                                    { content: '右旋转90度', value: 'rotate90', disabled: item.status === 0 },
                                    { content: '左旋转90度', value: 'rotate270', disabled: item.status === 0 },
                                ]"
                                    @click="handleSelectMore($event.value, item)"
                                >
                                    <t-button variant="text" size="small">
                                        <MoreIcon class="moreIcon" />
                                    </t-button>
                                </t-dropdown>
                            </div>
                        </template>
                    </div>

                    <div class="loading-more" v-if="filterParams.loading">
                        <t-loading size="small" />
                    </div>

                    <EmptyIcon v-else-if="filterParams.pageEnd && materialList?.length === 0" class="empty-container"
                               text="素材库暂无视频"
                               :description="type === 'local' ? '点击下面按钮去上传更多视频素材' : ''">
                        <template #retry v-if="type === 'local'">
                            <t-button theme="primary" @click="handleUpload">
                                <template #icon>
                                    <AddIcon />
                                </template>
                                本地上传
                            </t-button>
                        </template>
                    </EmptyIcon>
                </div>
            </div>

        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, readonly } from "vue";
import { AddIcon, MoreIcon } from "tdesign-icons-vue-next";
import { MessagePlugin } from "tdesign-vue-next";

import { useList } from "@/hooks/useList";
import Upload from "@/utils/upload";
import {
    getRecommendVideoMaterialList,
    getMyMaterialList,
    addMyMaterial,
    rotateMaterial,
    getSpeechVideoMaterialList,
} from "@/api/material";

import { getDate, convertDateParam, sleep } from "@/utils/fn";
import { VideoMaterial, PromptMap } from "../../common";
import { getMaterialStartTimeAndEndTime, getMaterialSelectId } from "../../common/utils";
import { verticalOptions } from "../../common/fields";

import TVideo from "@/components/TVideo/index.vue";
import EmptyIcon from "@/components/EmptyIcon/index.vue";
import SearchInput from "@/components/SearchInput/index.vue";
import SelectFolder from "@/components/SelectFolder/tree.vue";

import { StringHistoryKey } from "@/store/history";
import { getMaterialList } from "@/api/material/video.ts";
import { Folder } from "#/generate";

const props = withDefaults(
    defineProps<{
        // 素材来源类型: 企业素材、空镜素材、口播素材、本地素材
        type: "enterprise" | "personal" | "scene" | "speech" | "local";
        // 是否允许搜索
        allowSearch?: boolean;
        // 搜索历史存储key
        storageKey?: StringHistoryKey;
        // 视频规格
        vertical?: PromptMap["vertical"];

        // 视频时长，单位：毫秒
        duration?: number;
        // 已选素材列表
        selecteds?: VideoMaterial[];
        // 备选的素材列表
        materialOriginList?: VideoMaterial[];
        // 禁止选择的素材列表
        forbidMaterialList?: VideoMaterial[];
        // 占位符
        placeholder?: string;

        // 默认的搜索关键词
        defaultKeyword?: string;

        // 素材分类列表
        categoryList?: { id: string; name: string }[];

        folderTree?: Folder[];

        selectedFolderIds?: string[];

        // 基础请求参数
        baseRequestParams?: AnyObject;

        excludedIds?: string[];
    }>(),
    {
        allowSearch: false,
        placeholder: "请输入关键词搜索",
        categoryList: () => [],
        baseRequestParams: () => ({}),
    },
);

// 选中map
const selectedMap = computed(() => new Map(props.selecteds?.map((v) => [getMaterialSelectId(v, props.type === "speech"), true]) || []));

const emit = defineEmits<{
    (e: "select", value: VideoMaterial, keyword: string): void;
    (e: "autoSelect", value: VideoMaterial): void;
}>();

const keyword = ref(props.defaultKeyword || "");
const defaultFolderMap: { [key: string]: number[] } = {
    "personal": [-1],
    "enterprise": [-2],
};

let defaultFolderIds;

if (props.selectedFolderIds?.length) {
    defaultFolderIds = props.selectedFolderIds;
} else {
    defaultFolderIds = defaultFolderMap[props.type];
}

let { scrollContainer, filterParams, list, handleSearch, onFirstRequest } = useList<VideoMaterial>({
    init: true,
    listKey: props.type === "enterprise" || "scene" ? "list" : false,
    filterOptions: {
        // 外部带的基础请求参数
        ...props.baseRequestParams,
        pageSize: 20,
        duration: props.duration ? Math.ceil(props.duration / 1000) : undefined,
        keyword: "",
        vertical: props.vertical,
        __createTime__: [],

        // 获取素材类型, 个人素材库和企业素材库需要传入类型
        type: ["local", "enterprise"].includes(props.type) ? "video" : undefined,
    },
    filterApiParams(params) {
        let { __createTime__, folderIds, ...newParams } = params;

        filterParams.keyword = keyword.value;

        return {
            ...newParams,
            folderIds: !params.folderIds ? defaultFolderIds : [params.folderIds],
            ...convertDateParam(__createTime__),
            keyword: keyword.value,
        };
    },
    // 请求方法
    api: {
        enterprise: getMaterialList,
        personal: getMaterialList,
        local: getMyMaterialList,
        scene: getRecommendVideoMaterialList,
        speech: getSpeechVideoMaterialList,
    }[props.type],
    // 数据过滤
    itemFilter(item) {
        return {
            ...item,
            clipStart: item.clipStart || 0,
            clipDuration: item.clipDuration || item.duration,
        };
    },
});

// 可用的素材列表
const forbidMaterialMap = computed(() => {
    const map = new Map();

    props.forbidMaterialList?.forEach((item) => {
        map.set(item.materialId || item.id, item);
    });

    return map;
});

type materialLocalType = VideoMaterial & { uploadProgress?: number };
const materialList = computed(() => {
    if (props.type === "local" || !props.allowSearch || filterParams.keyword) {
        return list.value.filter((item) => !forbidMaterialMap.value.has(item.materialId || item.id)) as materialLocalType[];
    }

    return (props.materialOriginList?.filter?.((item) => !forbidMaterialMap.value.has(item.materialId || item.id)) || []) as materialLocalType[];
});

const handleUpload = async () => {
    let id = Math.random().toString(36).substring(2, 15);

    let uploadDataTemp: any;

    let { url, name } = await Upload.video("materialManagement", {
        onInput: () => {
            // 上传个临时数据
            list.value.unshift({ id, uploadProgress: 0 } as any);
            uploadDataTemp = list.value.find((v) => v.id === id)!;
        },
        onProgress: (percent: number) => (uploadDataTemp.uploadProgress = percent),
    });

    // 添加到我的素材
    let res = await addMyMaterial([url]);

    res.forEach((item: any) => {
        let index = list.value.findIndex((v) => v.id === id);

        if (item.id || item.materialId) {
            MessagePlugin.success(`上传成功`);
            list.value[index] = { ...item, clipStart: 0 };
        } else {
            list.value.splice(index, 1);
            MessagePlugin.error(`素材已存在，请勿重复上传！【${name}】`);
        }
    });

    pollingListStatus();
};

// 点击更多事件
const handleSelectMore = async (mod: string, item: any) => {
    switch (mod) {
        // 旋转
        case "rotate90":
        case "rotate270":
            let rotate = (item.rotate + (mod === "rotate90" ? 90 : 270)) % 360;

            // 记录之前的状态
            let status = item.status;
            // 更新当前状态为处理中
            item.status = 0;

            let [res] = await rotateMaterial([{
                materialId: item.materialId,
                rotate,
                materialType: item.materialType,
            }]);

            // 恢复之前的状态
            item.status = status;

            if (res.errMsg) {
                MessagePlugin.error(res.errMsg);
                break;
            }

            item.rotate = rotate;
            MessagePlugin.success("旋转成功");
            break;

        // // 删除
        // case "delete":
        //     handleDelete([item.id]);
        //     break;
    }
};

// 列表map
const listMap = computed(() => Object.fromEntries(list.value.map((item) => [item.id || item.materialId, item])));

// 轮询查询列表状态方法
const pollingListStatus = async () => {
    // 判断页面是否已卸载,如果卸载则停止轮询
    if (!document.body.contains(scrollContainer!.value) || !["enterprise", "local"].includes(props.type)) {
        return;
    }

    let ids = list.value.filter((item) => item.status === 0).map((item) => item.id || item.materialId);

    // 如果没有正在处理中的状态，则停止轮询
    if (!ids.length) {
        return;
    }

    await sleep(3);

    let res = await (props.type === "enterprise" ? getMaterialList : getMyMaterialList)({ ids } as any);
    if (props.type === "enterprise") {
        res = res.list;
    }

    res.forEach((item: any) => {
        if (item.status !== 0 && listMap.value[item.id || item.materialId]) {
            Object.assign(listMap.value[item.id || item.materialId], item);
        }
    });

    pollingListStatus();
};

// 请求完成后，轮询查询列表状态
onFirstRequest(() => {
    pollingListStatus();

    // 如果禁止选择的素材列表不为空 且 有默认关键词，则需要触发第一次请求
    if (props.forbidMaterialList?.length && props.defaultKeyword && materialList.value[0]) {
        emit("autoSelect", materialList.value[0]);
    }
});

defineExpose({
    // 素材列表
    materialList: readonly(materialList),
});
</script>

<style lang="less" scoped>
.material-selector-base {
    height: 100%;

    display: flex;
    flex-direction: row;

    > :deep(.directory-tree-wrapper) {
        padding: 10px;
    }

    > .material-container {
        background-color: #242b33;
        border-radius: 12px;
        padding: 10px;
        margin-left: 10px;

        > .search {
            margin-bottom: 15px;
        }

        > .filter-section {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;

            > .t-select__wrap,
            > .t-tree-select {
                width: 124px !important;
            }

            > .t-date-range-picker {
                flex: 1;
            }
        }

        > .material-container-wrapper {
            padding: 16px 20px;
            overflow: hidden;

            > .material-container {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
                gap: 20px;
                justify-content: space-between;
                width: 100%;

                > .material-item {
                    position: relative;

                    > .material-tags {
                        margin-top: 5px;
                        font-weight: 400;
                        font-size: 12px;
                        color: #ffffff;
                        line-height: 18px;
                    }

                    > .material-time {
                        font-weight: 400;
                        font-size: 10px;
                        color: rgba(255, 255, 255, 0.6);
                        line-height: 14px;

                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                    }

                    > .t-checkbox {
                        position: absolute;
                        top: 10px;
                        right: 10px;
                        pointer-events: none;
                    }

                    > .flag {
                        position: absolute;
                        top: 10px;
                        left: 10px;

                        font-weight: 500;
                        font-size: 10px;
                        color: #ffffff;
                        line-height: 1em;

                        padding: 4px 8px;
                        border-radius: 3px;
                        background-color: rgba(2, 86, 255, 0.8);
                    }

                    &.upload-btn {
                        aspect-ratio: 1 / 1;
                        background-color: #121314;
                        border-radius: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        font-size: 30px;
                    }

                    > .upload-progress-item {
                        background-color: #32383f;
                        aspect-ratio: 1 / 1;
                        border-radius: calc(100% * 0.042);
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;

                        > .t-loading {
                            font-size: 24px;
                        }

                        > .upload-progress {
                            font-weight: 400;
                            font-size: 14px;
                            color: rgba(255, 255, 255, 0.8);
                            margin-top: 13px;
                        }
                    }
                }

                > .loading-more {
                    grid-column: 1 / -1;
                    display: flex;
                    justify-content: center;
                    padding: 10px 0;
                }

                > .empty-container {
                    grid-column: 1 / -1;
                }
            }
        }
    }
}
</style>
