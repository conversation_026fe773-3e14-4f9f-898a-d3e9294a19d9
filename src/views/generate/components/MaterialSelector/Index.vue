<template>
    <PopupPanel :width="820" :title="maxCount ? '选择素材' : '选择口播素材'" @close="emit('update:visible', false)">
        <t-tabs :value="tabValue" @change="tabValue = $event">
            <t-tab-panel value="enterprise" label="企业素材">
                <MaterialSelectorBase
                    ref="enterpriseRef"
                    type="enterprise"
                    :selecteds="videoList"
                    @select="handleSelect"
                    :baseRequestParams="baseRequestParams"
                    :folder-tree="computedFolderTree"
                />
            </t-tab-panel>

            <t-tab-panel value="personal" label="个人素材">
                <MaterialSelectorBase
                    ref="personalRef"
                    type="personal"
                    :selecteds="videoList"
                    @select="handleSelect"
                    :baseRequestParams="baseRequestParams"
                    :folder-tree="computedFolderTree"
                />
            </t-tab-panel>
        </t-tabs>

        <template #footer>
            <div class="popup-panel-footer-container">
                <div class="popup-panel-footer-more">
                    <t-checkbox v-if="tabValue === 'enterprise' && !maxCount" :checked="checkAll" :indeterminate="indeterminate" @change="handleSelectAll">全选</t-checkbox>
                </div>

                <div class="popup-panel-footer-buttons">
                    <t-space>
                        <t-button theme="default" @click="emit('update:visible', false)">取消</t-button>
                        <t-button theme="primary" @click="handleConfirm">确定</t-button>
                    </t-space>
                </div>
            </div>
        </template>
    </PopupPanel>
</template>

<script lang="ts" setup>
import { ref, computed, inject, ComputedRef } from "vue";
import { cloneDeep } from "lodash";
import { TabValue } from "tdesign-vue-next";
import { MessagePlugin } from "tdesign-vue-next";

import { VideoMaterial } from "../../common";
import PopupPanel from "../PopupPanel.vue";
import MaterialSelectorBase from "./Base.vue";
import { Folder } from "#/generate";
import { getFolderList } from "@/api/folder";

const props = withDefaults(
    defineProps<{
        list: VideoMaterial[];
        maxCount?: number;
        // 基础请求参数
        baseRequestParams?: AnyObject;
    }>(),
    {
        list: () => [],
        maxCount: 0,
        baseRequestParams: () => ({}),
    },
);

const emit = defineEmits<{
    (e: "update:visible", visible: boolean): void;
    (e: "confirm", value: (Pick<VideoMaterial, "materialId" | "url" | "duration" | "width" | "height" | "rotate"> & { userMaterialId?: string })[]): void;
}>();

const enterpriseRef = ref();

const tabValue = ref<TabValue>("personal");

const _folderTree = inject<ComputedRef<Folder[]>>("folderTree");
let folderTree: Folder[] = [];
if (_folderTree){
    folderTree = cloneDeep(_folderTree.value)
}

getFolderList().then(res => folderTree = res);

const computedFolderTree = computed(() => {
    return folderTree?.filter((item) => {
        if (tabValue.value == "personal") {
            return item.id.toString() === "-1";
        } else {
            return item.id.toString() === "-2";
        }
    });
});

// 已选素材列表
const videoList = ref(cloneDeep(props.list).map((v) => ({ ...v, duration: v.duration * 1000 })));
// 已选素材ID列表
const selectedIds = computed(() => new Set(videoList.value.map((v) => v.materialId)));

// 选择素材
const handleSelect = (v: VideoMaterial) => {
    let item = { ...v, materialId: v.id || v.materialId };

    if (selectedIds.value.has(item.materialId)) {
        videoList.value = videoList.value.filter((v) => v.materialId !== item.materialId);
    } else {
        if (props.maxCount && videoList.value.length >= props.maxCount) {
            MessagePlugin.warning(`最多选择${props.maxCount}个素材`);
            return;
        }

        videoList.value.push(item);
    }
};

const handleConfirm = () => {
    emit(
        "confirm",
        videoList.value.map((v) => ({ url: v.url, materialId: v.materialId, userMaterialId: v.id, duration: v.duration / 1000, width: v.width, height: v.height, rotate: v.rotate })),
    );

    emit("update:visible", false);
};

// 全选
const checkAll = computed(() => !!enterpriseRef.value?.materialList?.length && enterpriseRef.value.materialList.every((v: VideoMaterial) => selectedIds.value.has(v.id || v.materialId)));
// 半选
const indeterminate = computed(() => enterpriseRef.value && !checkAll.value && enterpriseRef.value.materialList.some((v: VideoMaterial) => selectedIds.value.has(v.id || v.materialId)));
// 全选/全不选事件
const handleSelectAll = (checked: boolean) => {
    if (checked) {
        enterpriseRef.value.materialList.forEach((v: VideoMaterial) => {
            let item = { ...v, materialId: v.id || v.materialId };

            if (selectedIds.value.has(item.materialId)) {
                return;
            }

            videoList.value.push(item);
        });
    } else {
        let idsMap = enterpriseRef.value.materialList.reduce((acc: AnyObject, v: VideoMaterial) => {
            acc[v.id || v.materialId] = v;
            return acc;
        }, {});

        // 倒序删除
        for (let i = videoList.value.length - 1; i >= 0; i--) {
            if (idsMap[videoList.value[i].materialId]) {
                videoList.value.splice(i, 1);
            }
        }
    }
};
</script>

<style lang="less" scoped></style>
