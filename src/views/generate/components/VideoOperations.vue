<template>
    <t-dialog placement="center" header="视频操作" :visible="dialogVisible" width="auto" destroyOnClose :closeOnOverlayClick="false" @close="emit('close')">
        <div class="video-operations-wrapper">
            <div class="material-management">
                <t-tabs v-model:value="tabValue" @change="nextTick().then(WidgetViewRef?.cancelSelected)">
                    <t-tab-panel v-if="!isPatch" value="scene" label="空镜素材">
                        <MaterialSelectorBase
                            type="scene"
                            allow-search
                            storage-key="sceneHistory"
                            :vertical="vertical"
                            :defaultKeyword="scene"
                            :materialOriginList="materialOriginListLocal"
                            :duration="durationLocal"
                            :forbidMaterialList="forbidMaterialList"
                            :selecteds="materialListLocal"
                            :folder-tree="computedFolderTree"
                            :selected-folder-ids="props.categoryIds.map(e=>e.value)"
                            @select="handleSelect"
                            @autoSelect="handleAutoSelect"
                            :placeholder="`画面：${scene}`"
                            :baseRequestParams="{ taskId: draftId, scriptIndex: scriptIndex, excludedIds: excludedIds}"
                        />
                    </t-tab-panel>

                    <t-tab-panel v-if="isSpeechVideo" value="speech" label="口播素材">
                        <MaterialSelectorBase
                            type="speech"
                            allow-search
                            :materialOriginList="sceneMaterialOriginList"
                            :defaultKeyword="subtitleText"
                            :selecteds="sceneMaterialListLocal"
                            @select="handleSelectSpeech"
                            :baseRequestParams="{ taskId: draftId, scriptIndex: scriptIndex }"
                        />
                    </t-tab-panel>

                    <t-tab-panel value="enterprise" label="素材列表">
                        <MaterialSelectorBase
                            type="enterprise"
                            :selecteds="patchVideosLocal"
                            :baseRequestParams="{ taskId: draftId, scriptIndex: scriptIndex, excludedIds: excludedIds }"
                            :folder-tree="computedFolderTree"
                            :selected-folder-ids="props.categoryIds.map(e=>e.value)"
                            @select="handleSelect"
                        />
                    </t-tab-panel>

                    <t-tab-panel v-if="!isPatch" value="widget" label="分镜贴片">
                        <WidgetManagement
                            ref="WidgetManagementRef"
                            v-model:widgetId="lastWidgetId"
                            :hasWidget="!!WidgetViewRef?.widgets.length"
                            :showSystemTemplate="false"
                            @clickEmtpy="WidgetViewRef?.cancelSelected"
                            @select="WidgetViewRef?.autoAddWidget"
                            @clear="WidgetViewRef?.clearCanvas"
                            @loadTemplate="WidgetViewRef?.loadTemplate"
                        />
                    </t-tab-panel>
                </t-tabs>
            </div>

            <div class="video-preview">
                <div class="title">当前选中视频</div>

                <div class="video-wrapper">
                    <!-- 全屏的数字人 -->
                    <template v-if="canShowDigitalHuman(2)">
                        <img v-if="metaHuman.fullScreenPreviewUrl" :src="metaHuman.fullScreenPreviewUrl" class="fullMetaHuman" :class="{ system: isFission && digitalHumanConfigs.length === 0 }" mode="aspectFill" />
                        <EmptyIcon v-else class="empty-icon" :text="`暂无匹配的素材`" />
                    </template>

                    <!-- 空素材 -->
                    <EmptyIcon v-else-if="!currentPlayVideo" class="empty-icon" text="请选择视频" />

                    <!-- 有素材 -->
                    <template v-else>
                        <!-- 播放视频 -->
                        <video ref="videoRef" :src="currentPlayVideo?.url" autoplay @contextmenu.prevent @click.prevent :style="videoRotateStyles"></video>

                        <!-- 浮屏头像 -->
                        <img v-if="canShowDigitalHuman(1)" :src="metaHuman?.floatScreenPreviewUrl" class="floatAvatar" mode="aspectFill" />

                        <!-- 贴片预览 -->
                        <WidgetView
                            ref="WidgetViewRef"
                            :disabled="tabValue !== 'widget'"
                            :widget="widget"
                            :templateing="Boolean(WidgetManagementRef?.templateing)"
                            @autoGetTemplate="WidgetManagementRef?.autoGetTemplate"
                            @saveTemplate="WidgetManagementRef?.saveTemplate"
                        />

                        <template v-if="tabValue !== 'widget' && (digitalPersonSwitch !== 2 || isOriginalVideo || isSpeechVideo)">
                            <PlayCircleIcon v-if="!playing" @click="playing = true" />
                            <PauseCircleIcon v-else @click="playing = false" />
                        </template>
                    </template>
                </div>

                <!-- 前后贴视频 -->
                <div class="notProgressTip" v-if="tabValue === 'widget'" style="background-color: transparent">
                    <t-button theme="default" :disabled="!WidgetViewRef?.widgets.length" @click="WidgetViewRef?.saveTemplate">保存模板</t-button>
                </div>

                <!-- 前后贴视频 -->
                <div class="notProgressTip" v-else-if="isPatch">当前镜头是前后贴视频，不支持选择画面</div>

                <!-- 实拍视频 || 数字人 || 口播视频 不能选 -->
                <view class="notProgressTip" v-else-if="digitalPersonSwitch === 2 && (isOriginalVideo || isSpeechVideo || digitalPersonOpen)">{{ digitalPersonText }}视频不支持选择画面</view>

                <!-- 没有视频的时候。不展示 -->
                <div class="notProgressTip" v-else-if="videoList.length === 0"></div>

                <!-- 没有进度的提示 -->
                <div class="notProgressTip" v-else-if="!isSingle">当前镜头多个素材组合，不支持选择画面</div>

                <!-- 单素材 -->
                <div v-else class="progressBox" :style="varStyles" @mousedown="onProgressTouch">
                    <div class="bgBox">
                        <div class="time" :class="item.class + ' ' + index" v-for="(item, index) in timescale" :key="item.time">
                            <span class="num">{{ item.time }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 贴片编辑 -->
            <div class="property-editor" v-if="tabValue === 'widget'">
                <WidgetEditor :selectedObject="WidgetViewRef?.selectedObject" @clickEmtpy="WidgetViewRef?.cancelSelected" @change="WidgetViewRef?.autoUpdateActiveObject" />
            </div>

            <!-- 其他编辑 -->
            <div class="property-editor" v-else>
                <div class="popup-row" v-if="isPatch">
                    <div class="popup-row-title">素材原声开关</div>
                    <t-radio-group variant="primary-filled" class="full-width-radio-group" v-model="originAudioLocal">
                        <t-radio-button :value="true">开启原声</t-radio-button>
                        <t-radio-button :value="false">关闭原声</t-radio-button>
                    </t-radio-group>
                </div>

                <div class="popup-row" v-if="isOriginalVideo || digitalPersonOpen || isSpeechVideo">
                    <div class="popup-row-title">分镜展示形式</div>
                    <t-radio-group variant="primary-filled" v-model="digitalPersonSwitch" class="full-width-radio-group">
                        <t-radio-button v-for="item in digitalPersonSwitchList" :value="item.value" :disabled="item.disabled">{{ item.content }}</t-radio-button>
                    </t-radio-group>
                </div>

                <div class="popup-row" v-if="showTransition">
                    <div class="popup-row-title">
                        <span>分镜入场特效</span>

                        <div v-if="formConfig.transitionList.length > 15" class="tip" @click="entryEffectsExpanded = !entryEffectsExpanded">点击{{ entryEffectsExpanded ? "收起" : "更多" }}</div>
                    </div>

                    <GridList class="entry-effects-wrapper" v-model:active="entryEffectValue" :empty-item="{ title: '无', value: 0 }" :list="formConfig.transitionList.filter((_, i) => entryEffectsExpanded || i < 15)" />
                </div>

                <!-- <div class="popup-row"> -->
                <!-- <div class="popup-row-title">画面变速</div> -->
                <!--  -->
                <!-- <SliderControl class="SliderControl" :value="20"></SliderControl> -->
                <!-- </div> -->
            </div>
        </div>

        <template #footer>
            <div class="video-operations-footer">
                <div>
                    <template v-if="!isPatch">
                        <span style="margin-right: 8px">当前字幕：{{ subtitleText }}</span>
                        镜头时长为{{ sceneDuration.toFixed(1) }}秒
                    </template>
                </div>

                <t-space>
                    <t-button theme="default" @click="emit('close')">取消</t-button>
                    <t-button theme="primary" @click="handleConfirm" :disabled="!currentPlayVideo && digitalPersonSwitch !== 2">保存</t-button>
                </t-space>
            </div>
        </template>
    </t-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch, nextTick, inject, ComputedRef } from "vue";
import { PlayCircleIcon, PauseCircleIcon } from "tdesign-icons-vue-next";
import { TabValue } from "tdesign-vue-next";
import { useMediaControls } from "@vueuse/core";
import { cloneDeep } from "lodash";

import { getRecommendVideoMaterialList } from "@/api/material";
import { log2java } from "@/common/log";
import { useMouseDownDrag, useRotateVideo } from "@/hooks/useDocument";
import { debounce, secondToDate, generateDialogVisible } from "@/utils/fn";
import type { Folder, VideoOperationsProps, VideoOperationsResult, Widget } from "#/generate";
import { getOriginalVideoClip } from "../common/utils";
import { useDigitalPersonDisplay } from "../common/hooks/useDigitalPersonDisplay";
import { filterMaterial } from "../common/scripts";
import { FormConfig, VideoMaterialScript, VideoMaterial } from "../common";

// import SliderControl from "@/components/SliderControl/index.vue";
import MaterialSelectorBase from "./MaterialSelector/Base.vue";
import EmptyIcon from "@/components/EmptyIcon/index.vue";
import WidgetManagement from "./WidgetOperations/Management.vue";
import WidgetView from "./WidgetOperations/View.vue";
import WidgetEditor from "./WidgetOperations/Editor.vue";
import GridList from "./GridList.vue";

const props = withDefaults(defineProps<VideoOperationsProps>(), {
    forbidMaterialList: () => [],
    materialOriginList: () => [],
    originalVideo: () => [],
    categoryIds: () => [],
    patchVideos: () => [],
});

const emit = defineEmits<{
    (e: "close"): void;
    (e: "confirm", value: VideoOperationsResult): void;
}>();

// 解决esc不生效问题，需要延迟0秒再展示
const dialogVisible = generateDialogVisible();

// 表单配置
const formConfig = inject<ComputedRef<FormConfig>>("formConfig")!;

const folderTree = inject<ComputedRef<Folder[]>>("folderTree")!;

const computedFolderTree = computed(() => {
    const filterTree = (nodes: Folder[]): Folder[] => {
        const folderIds = props.categoryIds.map(e=>e.value)
        return nodes.reduce((filtered: Folder[], node) => {
            const matches = folderIds.indexOf(node.id.toString()) !== -1;
            const filteredChildren = node.children ? filterTree(node.children) : [];

            // 如果当前节点匹配或有匹配的子节点，则包含此节点
            if (matches || filteredChildren.length > 0) {
                filtered.push({
                    ...node,
                    children: filteredChildren.length > 0 ? filteredChildren : node.children,
                });
            }

            return filtered;
        }, []);
    };

    return filterTree(folderTree.value)
});

// 入场特效
const entryEffectValue = ref(props.transitionId || 0);

// 入场特效收起展开状态
const entryEffectsExpanded = ref(false);

// 贴片管理器
const WidgetManagementRef = ref<InstanceType<typeof WidgetManagement>>();
// 贴片预览
const WidgetViewRef = ref<InstanceType<typeof WidgetView>>();
// 贴片ID
const lastWidgetId = ref(props.widget?.id || "");

// 素材库Tab: 全屏口播的时候，默认选中口播素材tab，否则选中空间素材tab
const tabValue = ref<TabValue>();
// 异步设置tabValue，解决组件无法展示选中效果问题
setTimeout(() => {
    if (props.isPatch) {
        tabValue.value = "enterprise";
    } else if (props.isSpeechVideo && props.digitalPersonDisplay === 2) {
        tabValue.value = "speech";
    } else {
        tabValue.value = "scene";
    }
}, 100);

// 音乐控制
const videoRef = ref();
const { playing, currentTime, muted } = useMediaControls(videoRef);

// 播放视频旋转样式
const { videoRotateStyles } = useRotateVideo({ rotate: computed(() => currentPlayVideo.value.rotate || 0), videoWidth: computed(() => 273) });

const subtitleText = computed(() => props.subtitles.map((v) => v.text).join("，"));

// 数字人开关
let digitalPersonSwitch = ref<VideoMaterialScript["digitalPersonDisplay"]>(cloneDeep(props.digitalPersonDisplay) || -1);

// 数字人全屏的时候，静音
watch(
    digitalPersonSwitch,
    () => {
        muted.value = digitalPersonSwitch.value !== 2;

        // 一进来的时候初始化时，currentPlayVideo还没有，所以需要处理一下
        try {
            if (currentPlayVideo.value) {
                playing.value = true;
            }
        } catch (error) {}

        // 如果口播，做切换tab操作
        if (props.isSpeechVideo) {
            if (digitalPersonSwitch.value === -1 && tabValue.value === "speech") {
                tabValue.value = "scene";
            } else if (digitalPersonSwitch.value === 2 && tabValue.value !== "speech") {
                tabValue.value = "speech";
            }
        }
    },
    { immediate: true },
);

// 镜头时长（单位为毫秒）
const durationLocal = ref(props.duration);

// 镜头时长（转换为秒）
const sceneDuration = computed(() => durationLocal.value / 1000);

// 缓存到本地的空镜素材列表
const materialListLocal = ref<VideoMaterial[]>(cloneDeep(props.materialList) || []);

// 缓存到本地的备选空镜素材列表
const materialOriginListLocal = ref<VideoMaterial[]>(cloneDeep(props.materialOriginList) || []);

// 缓存到本地的口播素材列表
const sceneMaterialListLocal = ref<VideoMaterial[]>(cloneDeep(props.sceneMaterialList) || []);

// 缓存到本地的前后贴视频
const patchVideosLocal = ref<VideoMaterial[]>(cloneDeep(props.patchVideos));

// 缓存原声开启状态, 前后贴视频默认开启原声，其他默认关闭
const originAudioLocal = ref(props.patchVideos[0]?.originAudio ?? props.isPatch);

// 数字人展示
const { digitalPersonText, digitalPersonSwitchList, canShowDigitalHuman, metaHuman } = useDigitalPersonDisplay({
    props,
    sceneStart: computed(() => props.sceneStart),
    materialList: materialListLocal,
    sceneMaterialList: sceneMaterialListLocal,
    digitalPersonDisplay: computed(() => digitalPersonSwitch.value),
});

// 视频列表，将毫秒转换成秒
let videoList = computed(() => {
    // 转换方法
    const convert = (v: VideoMaterial) => {
        let clipStart = v.clipStart / 1000 || 0;
        let clipDuration = (v.clipDuration || durationLocal.value) / 1000;

        return { ...v, startTime: clipStart, endTime: clipStart + clipDuration, duration: v.duration / 1000 };
    };

    // 前后贴视频
    if (props.isPatch) {
        return patchVideosLocal.value.map(convert);
    }

    // 全屏状态下
    if (digitalPersonSwitch.value === 2) {
        // 如果是口播
        if (props.isSpeechVideo) {
            return sceneMaterialListLocal.value.map(convert);
        }

        // 实拍视频
        if (props.isOriginalVideo) {
            return getOriginalVideoClip(props.sceneStart, props.sceneEnd, props.originalVideo).map(convert);
        }
    }

    // 空镜素材
    return materialListLocal.value.map(convert);
});

// 是否只有一个视频
let isSingle = computed(() => materialListLocal.value.length === 1);

// 第一个视频
let firstVideo = computed(() => videoList.value[0]);

// 时间刻度尺
let timescale = computed(() => {
    let duration = Math.ceil(materialListLocal.value[0]?.duration / 1000) || 0;

    // 超过5秒，按长短刻度展示
    if (firstVideo.value.clipDuration / 1000 > 5) {
        return Array.from({ length: duration }, (_, i) => {
            let isLong = i % 5 === 0 || i === duration - 1;
            return { class: isLong ? "longTick" : "shortTick", time: isLong ? secondToDate(i, "mm:ss") : "" };
        });
    }

    return Array.from({ length: duration }, (_, i) => ({ class: "longTick", time: secondToDate(i, "mm:ss") }));
});

// 触摸事件数据
let touchstartData = reactive({
    // 记录原始的x坐标
    x: -1,
    // 记录原本的播放时间
    time: firstVideo.value?.startTime || 0,
    // 记录触摸开始时间
    _t: firstVideo.value?.startTime || 0,
    // 显示更新时间按钮
    showBtn: false,
});

const progressActiveWidth = 128;
const widthUnit = progressActiveWidth / sceneDuration.value;

const onProgressTouch = useMouseDownDrag({
    onStart: (e) => {
        Object.assign(touchstartData, {
            x: e.clientX,
            _t: touchstartData.time,
            state: true,
        });
    },
    onMove: (e) => {
        let t = (e.clientX - touchstartData.x) / widthUnit;
        let time = Math.max(0, Math.min(touchstartData._t - t, materialListLocal.value[0]?.duration / 1000 - sceneDuration.value));

        // 更新播放时间
        touchstartData.time = Number(time.toFixed(1));

        // 跳转到指定时间
        currentTime.value = touchstartData.time;

        if (playing.value) {
            // 暂停视频播放
            playing.value = false;
        }
    },
    onEnd: () => {
        materialListLocal.value[0].clipStart = touchstartData.time * 1000;
        touchstartData.x = -1;

        // 开始视频播放
        playing.value = true;
    },
});

// 样式变量
const varStyles = computed(() => {
    // 0秒时，进度条的起点位置
    let progressLeftOffset = -(progressActiveWidth / 2);

    let lastDuration = (materialListLocal.value[0]?.duration / 1000) % 1;

    return {
        // 选中的宽度
        ["--progressActiveWidth"]: `${progressActiveWidth}px`,
        // 每秒视频占的容器宽度
        ["--widthUnit"]: `${widthUnit}px`,
        // 当前进度条左边距
        ["--leftMargin"]: `${progressLeftOffset - touchstartData.time * widthUnit}px`,
        // 最后一秒的宽度
        ["--lastWidth"]: `${(lastDuration || 1) * widthUnit}px`,
    };
});

// 当前播放的视频下标
let currentPlayIndex = ref(-1);

// 当前播放的视频
let currentPlayVideo = computed(() => videoList.value[currentPlayIndex.value]);

// 播放第一个视频
const playFirstVideo = () => {
    // 设置当前播放的视频的下标
    currentPlayIndex.value = 0;
    // 设置当前播放的视频的时间
    currentTime.value = firstVideo.value.startTime;
    // 开始播放
    playing.value = true;
};

// 切换播放状态
const tooglePlay = (index: number) => {
    if (debounce("tooglePlay")) {
        return;
    }

    // 如果没有播放的视频了
    if (!videoList.value[index]) {
        // 暂停视频播放
        playing.value = false;
        return;
    }

    // 更新当前播放的视频
    currentPlayIndex.value = index;
};

if (!props.isPatch) {
    // 监听播放进度
    watch(playing, (playState) => {
        if (playState) {
            let time = isSingle.value && !props.isSpeechVideo && !props.isOriginalVideo ? touchstartData.time : currentPlayVideo.value.startTime;

            if (Math.abs(currentTime.value - time) > 0.1) {
                // 如果只有一个视频，则使用触摸事件的播放时间 否则使用视频的播放时间
                currentTime.value = time;
            }
        }
    });

    // 监听播放进度
    watch(currentTime, () => {
        // 如果播放的视频超采用范围
        if (touchstartData.x === -1 && currentTime.value >= currentPlayVideo.value?.endTime) {
            if (isSingle.value) {
                // 暂停视频播放
                playing.value = false;
                // 跳转到指定时间
                currentTime.value = firstVideo.value.startTime;
            } else {
                // 切换视频播放
                tooglePlay(currentPlayIndex.value + 1);
            }
        }
    });
}

// 默认播放一次
tooglePlay(0);

// 选择口播素材
const handleSelectSpeech = (item: VideoMaterial, keyword: string) => {
    // 替换素材时的上报埋点
    reportReplaceMaterial("口播", item, keyword);

    // 选择口播视频的时候，如果是关闭数字人的，则开启
    if (digitalPersonSwitch.value === -1) {
        digitalPersonSwitch.value = 2;
    }

    // 更新分镜时长
    durationLocal.value = item.clipDuration;

    // 替换选择的素材
    sceneMaterialListLocal.value = [item];

    // 过滤已选空镜素材
    materialListLocal.value = materialListLocal.value.filter((v) => filterMaterial(v, durationLocal.value));

    // 过滤备选空镜素材
    materialOriginListLocal.value = materialOriginListLocal.value.filter((v) => filterMaterial(v, durationLocal.value));

    // 播放第一个视频
    playFirstVideo();

    // 如果过滤后，没有备选空镜素材，则重新获取一批
    if (!materialOriginListLocal.value.length) {
        getRecommendVideoMaterialList({
            pageSize: 10,
            duration: Math.ceil(durationLocal.value / 1000),
            keyword: props.scene,
            folderIds: props.categoryIds.map((v) => v.value),
            scriptIndex: props.scriptIndex,
        }).then((res) => {
            materialOriginListLocal.value = res;
        });
    }
};

// 替换素材时的上报埋点
const reportReplaceMaterial = (materialType: string, item: VideoMaterial, keyword: string | null) => {
    if (keyword === null) {
        return;
    }

    // 分镜编排-编辑视频-替换素材
    log2java.CREATE_FORM_EDIT_VIDEO_REPLACE_MATERIAL({ draftId: props.draftId, searchWord: keyword, materialType, materialId: item.materialId || item.id, subtitle: subtitleText.value, boardNo: props.scriptIndex + 1 });
};

// 选择空镜素材
const handleSelect = (item: VideoMaterial, keyword: string | null) => {
    // 前后贴视频
    if (props.isPatch) {
        patchVideosLocal.value = [item];
        touchstartData.time = 0;
        // 播放第一个视频
        playFirstVideo();
        return;
    }

    // 替换素材时的上报埋点
    reportReplaceMaterial(item.materialId ? "空镜" : "本地", item, keyword);

    // 选择数字人的时候，如果是开启全屏数字人的，则关闭
    if (digitalPersonSwitch.value === 2) {
        digitalPersonSwitch.value = -1;
    }

    // 替换选择的素材
    materialListLocal.value = [item];
    // 设置触摸事件的播放时间
    touchstartData.time = materialListLocal.value[0].clipStart / 1000 || 0;

    // 播放第一个视频
    playFirstVideo();
};

// 请求后自动选掼第一个素材
const handleAutoSelect = (item: VideoMaterial) => {
    // 裂变不需要默认选中
    if (props.isFission) {
        return;
    }

    if (!currentPlayVideo.value) {
        handleSelect(item, null);
    }
};

const handleConfirm = () => {
    // 分镜编排-编辑视频-保存
    log2java.CREATE_FORM_EDIT_VIDEO_SAVE({
        draftId: props.draftId,
        subtitle: subtitleText.value,
        boardNo: props.scriptIndex + 1,
        emptyMaterialId: materialListLocal.value[0]?.materialId || materialListLocal.value[0]?.id,
        oralMaterialId: sceneMaterialListLocal.value[0]?.materialId,
        displayType: digitalPersonSwitchList.value.find((v) => v.value === digitalPersonSwitch.value)?.content,
    });

    // 设置前后贴视频的原声
    patchVideosLocal.value.forEach((v) => (v.originAudio = originAudioLocal.value));

    emit("confirm", {
        digitalPersonDisplay: digitalPersonSwitch.value,
        duration: durationLocal.value,
        materialList: materialListLocal.value,
        materialOriginList: materialOriginListLocal.value,
        sceneMaterialList: sceneMaterialListLocal.value,
        patchVideos: patchVideosLocal.value,
        widget: { id: lastWidgetId.value, widgets: cloneDeep(WidgetViewRef.value?.widgets as Widget[]) },
        transitionId: entryEffectValue.value,
    });

    emit("close");
};
</script>

<style lang="less" scoped>
.video-operations-wrapper {
    height: 614px;
    display: flex;
    gap: 20px;
    overflow: hidden;

    > .material-management {
        width: 808px;
    }

    > .video-preview {
        width: 321px;
        height: 100%;
        display: flex;
        flex-direction: column;
        background-color: #121314;
        border-radius: 20px;
        padding: 26px 24px;
        gap: 10px;

        > .title {
            font-weight: 700;
            font-size: 16px;
            color: #ffffff;
            line-height: 23px;
            text-align: center;
        }

        > .video-wrapper {
            flex: 1;
            width: 100%;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;

            > .t-icon {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 53px;
                cursor: pointer;
            }

            > .floatAvatar {
                position: absolute;
                top: 150px;
                left: 15px;
                width: 1em;
                height: 1em;
                font-size: 80px;
                border-radius: 50%;
                box-shadow: 0 0 12px rgba(0, 0, 0, 0.5);
                background-color: #fff;
            }

            > .fullMetaHuman {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 100%;
                height: 100%;

                &.system {
                    width: 8cqw;
                    height: 8cqw;
                    border-radius: 50%;
                }
            }

            > .empty-icon {
                margin-top: 0;
            }
        }

        .progress() {
            width: 100%;
            height: 32px;
            background-color: rgba(52, 57, 63, 0.9);
            border-radius: 3px;
            overflow: hidden;
        }

        > .notProgressTip {
            .progress();
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 48rpx;
            color: rgba(255, 255, 255, 0.5);
        }

        > .progressBox {
            .progress();
            position: relative;

            &::after {
                content: "";
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
                box-sizing: border-box;
                width: var(--progressActiveWidth);
                height: 100%;
                background-color: rgba(2, 86, 255, 0.2);
                border-radius: 3px;
                border: 1px solid rgba(255, 255, 255, 0.8);
                pointer-events: none;
            }

            > .bgBox {
                position: absolute;
                top: 0;
                left: 50%;
                display: flex;
                height: 100%;
                border-radius: inherit;
                transform: translateX(var(--leftMargin));
                cursor: pointer;

                > .time {
                    position: relative;
                    width: var(--widthUnit);
                    height: 100%;
                    margin-top: 5px;
                    color: rgba(255, 255, 255, 0.2);
                    border-top: 1px solid rgba(255, 255, 255, 0.2);
                    user-select: none;

                    &:last-child {
                        width: var(--lastWidth);
                    }

                    &::after {
                        position: absolute;
                        top: -1px;
                        left: -1px;
                        width: 2px;
                        background-color: currentColor;
                        content: "";
                    }

                    &.longTick::after {
                        height: 10px;
                        background: #d8d8d8;
                    }

                    &.shortTick::after {
                        height: 5px;
                    }

                    > .num {
                        position: absolute;
                        top: 12px;
                        left: 0;
                        transform: translateX(-50%);
                        white-space: nowrap;

                        font-weight: 400;
                        font-size: 10px;
                        color: rgba(255, 255, 255, 0.8);
                        line-height: 12px;
                    }
                }
            }
        }
    }

    > .property-editor {
        width: 334px;
        overflow-y: overlay;

        > .popup-row {
            + .popup-row {
                margin-top: 20px;
            }

            > .popup-row-title {
                font-weight: 500;
                font-size: 14px;
                color: rgba(255, 255, 255, 0.9);
                line-height: 22px;
                margin-bottom: 10px;

                display: flex;
                justify-content: space-between;
                align-items: center;

                > .tip {
                    cursor: pointer;

                    font-weight: 400;
                    font-size: 12px;
                    color: rgba(255, 255, 255, 0.8);
                    line-height: 22px;
                }
            }

            > .entry-effects-wrapper {
                padding: 10px;
                background-color: #242b33;
                border-radius: 3px;
            }
        }
    }
}

.video-operations-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40px;

    text-align: left;
}
</style>
