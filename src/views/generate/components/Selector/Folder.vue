<template>
    <PopupPanel :title="`选择文件夹`" @close="emit('close')">
        <div class="popup-body">
            <div class="popup-row">
                <t-input
                    v-model="searchKeyword"
                    placeholder="搜索文件夹..."
                    clearable
                    @input="handleSearch"
                >
                    <template #suffixIcon>
                        <SearchIcon />
                    </template>
                </t-input>

                <div class="search-history-panel" v-if="storageKey && historyList.length">
                    <span class="history-title">历史搜索</span>

                    <div class="search-history-list">
                        <div class="history-item" v-for="item in historyList" :key="item.id"
                             @click="handleHistoryClick(item.id)">{{ item.title }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="popup-row tree-wrapper">
                <t-tree
                    :data="filteredOptions"
                    :keys="{ value: 'id', label: 'name', children: 'children' }"
                    :checkable="true"
                    v-model:value="checkedKeys"
                    valueMode="parentFirst"
                    :expanded="expandedKeys"
                    @expand="handleTreeExpand"
                >
                    <template #label="{ node }">
                        <span class="tree-node-label">
                            {{ node.data.name }}
                        </span>
                    </template>
                </t-tree>
            </div>
        </div>

        <template #footer>
            <div class="popup-panel-footer-container">
                <div class="popup-panel-footer-more">
                    <div class="item">
                        <span class="title required">素材规格</span>

                        <t-space size="small">
                            <t-button size="small" v-for="item in verticalOptions" :key="item.value"
                                      :theme="verticalValue === item.value ? 'primary' : 'default'"
                                      @click="verticalValue = item.value">
                                {{ item.label }}
                            </t-button>
                        </t-space>
                    </div>

                    <t-divider layout="vertical" />

                    <div class="item">
                        <span class="title">开启素材原声</span>
                        <t-switch size="large" v-model="originAudioValue"></t-switch>
                    </div>
                </div>

                <div class="popup-panel-footer-buttons">
                    <t-space>
                        <t-button theme="default" @click="emit('close')">取消</t-button>
                        <t-button theme="primary" @click="handleConfirm">确定</t-button>
                    </t-space>
                </div>
            </div>
        </template>
    </PopupPanel>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, inject, ComputedRef } from "vue";
import { SearchIcon } from "tdesign-icons-vue-next";
import { cloneDeep } from "lodash";

import { useHistoryStore, ObjectHistoryKey } from "@/store/history";
import { treeToMap } from "@/utils/fn";
import { PromptMap } from "../../common";
import { verticalOptions } from "../../common/fields";
import { getFolderList } from "@/api/folder";

import PopupPanel from "../PopupPanel.vue";
import { Folder } from "#/generate";

const props = defineProps<{
    value: PromptMap["categoryIds"];
    vertical: PromptMap["vertical"];
    originAudio: PromptMap["originAudio"];
    storageKey?: ObjectHistoryKey;
}>();

const emit = defineEmits<{
    (e: "close"): void;
    (e: "confirm", event: {
        value: PromptMap["categoryIds"];
        vertical: PromptMap["vertical"];
        originAudio: PromptMap["originAudio"]
    }): void;
}>();

// 历史记录
const historyStore = useHistoryStore();

// 文件夹数据
const options = ref<Folder[]>([]);
const optionsMap = computed(() => treeToMap<Folder>(options.value, { parentIdKey: "pid", idKey: "id" }));

// 历史记录列表
const historyList = computed(() => (props.storageKey ? historyStore[props.storageKey].filter((v) => optionsMap.value.get(v.id)).filter((_, i) => i < 6) : []));

// 素材规格
const verticalValue = ref(props.vertical);
// 素材原声
const originAudioValue = ref(props.originAudio);

// 搜索相关
const searchKeyword = ref("");

// 树形控件相关
const checkedKeys = ref<string[]>([]);
const expandedKeys = ref<string[]>([]);

// 过滤后的文件夹数据
const filteredOptions = computed(() => {
    if (!searchKeyword.value.trim()) {
        return options.value;
    }

    const keyword = searchKeyword.value.toLowerCase();

    // 递归过滤函数
    const filterTree = (nodes: Folder[]): Folder[] => {
        return nodes.reduce((filtered: Folder[], node) => {
            const matchesKeyword = node.name.toLowerCase().includes(keyword);
            const filteredChildren = node.children ? filterTree(node.children) : [];

            // 如果当前节点匹配或有匹配的子节点，则包含此节点
            if (matchesKeyword || filteredChildren.length > 0) {
                filtered.push({
                    ...node,
                    children: filteredChildren.length > 0 ? filteredChildren : node.children,
                });
            }

            return filtered;
        }, []);
    };

    return filterTree(options.value);
});

// 获取文件夹数据
onMounted(async () => {
    const _folderTree = inject<ComputedRef<Folder[]>>("folderTree")!;

    const folderTree: Folder[] = _folderTree ? cloneDeep(_folderTree.value) : await getFolderList();

    options.value = cloneDeep(folderTree);

    // 初始化选中的keys
    checkedKeys.value = props.value.map(item => item.value);

    // 默认展开第一层
    expandedKeys.value = folderTree.map(item => item.id);
});

// 搜索处理
const handleSearch = () => {
    // 搜索时自动展开所有匹配的节点
    if (searchKeyword.value.trim()) {
        const expandAll = (nodes: Folder[]): string[] => {
            let ids: string[] = [];
            nodes.forEach(node => {
                ids.push(node.id);
                if (node.children) {
                    ids.push(...expandAll(node.children));
                }
            });
            return ids;
        };
        expandedKeys.value = expandAll(filteredOptions.value);
    } else {
        // 清空搜索时恢复默认展开状态
        expandedKeys.value = options.value.map(item => item.id);
    }
};

// 历史记录点击处理
const handleHistoryClick = (id: string) => {
    const item = optionsMap.value.get(id);
    if (item) {
        // 设置搜索关键词为历史项的名称
        searchKeyword.value = item.name;
        handleSearch();
        if (checkedKeys.value.indexOf(item.id) == -1) {
            checkedKeys.value.push(id);
        }

        // 添加到历史记录
        if (props.storageKey) {
            historyStore.addObjectHistory(props.storageKey, { id: item.id, title: item.name });
        }
    }
};

// 树形控件展开事件
const handleTreeExpand = (expandedValues: string[]) => {
    expandedKeys.value = expandedValues;
};

// 基于选中的keys生成categoryIds格式的数据
const selectedFolders = computed<PromptMap["categoryIds"]>(() => {
    return checkedKeys.value.map(id => {
        const item = optionsMap.value.get(id);
        if (props.storageKey && item) {
            historyStore.addObjectHistory(props.storageKey, { id: item.id, title: item.name });
        }
        return item ? { value: id, title: item.name, pid: item.pid } : { value: id, title: "", pid: undefined };
    }).filter(item => item.title);
});


const handleConfirm = () => {
    emit("confirm", {
        value: cloneDeep(selectedFolders.value),
        vertical: verticalValue.value,
        originAudio: originAudioValue.value,
    });
    emit("close");
};
</script>

<style lang="less" scoped>
@import "../../common/css/popup.less";

.search-history-panel {
    user-select: none;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;

    > .history-title {
        color: #808695;
        font-size: 12px;
        margin-right: 8px;
    }

    > .search-history-list {
        flex: 1;

        white-space: nowrap;
        overflow-x: auto;

        > .history-item {
            display: inline-block;
            align-items: center;
            padding: 4px 8px;
            background-color: #363e4f;
            border-radius: 3px;
            cursor: pointer;
            transition: background-color 0.2s;

            color: #c5c6c9;
            font-size: 12px;

            & + .history-item {
                margin-left: 8px;
            }

            &:hover {
                background-color: #404b61;
            }
        }
    }
}

.tree-wrapper {
    flex: 1;
    overflow: hidden;
    border-radius: 12px;
    background-color: #242b33;
    padding: 20px;


    .tree-node-label {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    :deep(.t-tree) {
        background-color: transparent;
        color: #ffffff;

        .t-tree__list {
            background-color: transparent;
        }

        .t-tree__item {
            color: #ffffff;

            &:hover {
                background-color: rgba(255, 255, 255, 0.05);
            }

            .t-tree__item--active {
                background-color: rgba(0, 82, 217, 0.1);
            }
        }

        .t-tree__label {
            color: #ffffff;
        }

        .t-tree__icon {
            color: #ffffff;
        }

        .t-checkbox {
            .t-checkbox__input {
                border-color: #404b61;
            }

            &.t-is-checked .t-checkbox__input {
                background-color: #0052d9;
                border-color: #0052d9;
            }
        }
    }
}

.popup-panel-footer-container {
    padding-left: 0 !important;

    > .popup-panel-footer-more {
        display: flex;
        align-items: center;

        > .item {
            > .title {
                margin-right: 10px;

                font-weight: 400;
                font-size: 12px;
                line-height: 22px;

                &.required:before {
                    content: "* ";
                    color: rgba(234, 0, 0, 1);
                }
            }
        }
    }
}
</style>
