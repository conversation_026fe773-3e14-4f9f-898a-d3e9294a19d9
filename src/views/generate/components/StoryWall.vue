<template>
    <div class="generate-wrapper">
        <div class="content-wrapper">
            <WidgetItem :widgets="globalWidget?.widgets" :disabled="isPreview" :twoLineMode="twoLineMode" @edit="showWidgetEdit = true" @delete="handleDeleteWidget" />

            <PatchItem
                :id="`story-item-${-99999}`"
                :scriptIndex="-99999"
                :active="activeScriptIndex === -99999"
                :disabled="isPreview"
                :item="beforeScriptModelValue.materialList[0]"
                :twoLineMode="twoLineMode"
                patchText="前贴"
                @edit="handleEditPatch(-99999, -1, beforeScriptModelValue.materialList)"
                @select="activeScriptIndex = -99999"
                @delete="handleDeletePatch(-99999)"
            />

            <StoryItem
                v-for="(item, index) in scriptsModelValue"
                :active="activeScriptIndex === index"
                :id="`story-item-${index}`"
                :key="index"
                :draftId="formData.draftId!"
                :scriptIndex="index"
                :item="item"
                @update:item="scriptsModelValue[index] = $event"
                v-model:subtitlePositionY="subtitlePositionY"
                :disabled="isPreview"
                :isOriginalVideo="isOriginalVideo"
                :isSpeechVideo="isSpeechVideo"
                :originalVideo="formData.promptMap.originalVideo"
                :digitalPersonOpen="formData.promptMap.digitalPersonOpen"
                :categoryIds="formData.promptMap.categoryIds"
                :subtitles="scriptSubtitles[index].children"
                :digitalHumanConfigs="digitalHumanConfigs"
                :subtitleOff="formData.promptMap.subtitleOff"
                :rotateingList="rotateingList"
                :twoLineMode="twoLineMode"
                :widget="item.widgets?.[0]"
                :showTransition="beforeScriptModelValue.materialList.length > 0 || index > 0"
                @change="emit('change')"
                @edit="handleVideoOperation(item, index, 0, beforeScriptModelValue.materialList.length > 0 || index > 0)"
                @select="activeScriptIndex = index"
                @editSubtitle="emit('editSubtitle')"
                @rotate="handleRotate(index)"
            />

            <PatchItem
                :id="`story-item-${99999}`"
                :scriptIndex="99999"
                :active="activeScriptIndex === 99999"
                :disabled="isPreview"
                :item="afterScriptModelValue.materialList[0]"
                :twoLineMode="twoLineMode"
                patchText="后贴"
                @edit="handleEditPatch(99999, -1, afterScriptModelValue.materialList)"
                @select="activeScriptIndex = 99999"
                @delete="handleDeletePatch(99999)"
            />
        </div>

        <div v-if="isPreview" class="footer-wrapper single-mode">
            <t-button @click="emit('clone')" size="large">
                {{ previewMode === "video" ? "重新创作" : "使用案例" }}
            </t-button>
        </div>

        <div v-else class="footer-wrapper">
            <div class="footer-left">
                <div class="item">
                    <div class="title required">选择视频目录</div>
                    <SelectDirectoryCascader class="value" type="video" v-model:value="videoCategoryId" @change="videoCategoryPath = $event.content" />
                </div>
            </div>

            <div class="footer-right">
                <t-button v-if="!previewMode" theme="default" @click="backToSubtitleEdit(emit)">
                    <template #icon><RollbackIcon /></template>
                    返回编排字幕
                </t-button>

                <t-tooltip :disabled="!missingMaterialScripts.length" placement="top-right">
                    <template #content>
                        <InfoCircleFilledIcon style="margin-right: 2px; vertical-align: middle; color: rgba(54, 110, 244, 1)" />
                        {{
                            `${missingMaterialScripts
                                .filter((_, i) => i < 5)
                                .map((scriptIndex: number) => "分镜" + (scriptIndex + 1))
                                .join(", ")}${missingMaterialScripts.length > 5 ? "..." : ""}的素材缺失！`
                        }}
                    </template>

                    <GradientButton :disabled="gradientButtonDisabled" :loading="loading" @click="emit('confirm', { videoCategoryPath, generateVideoCount })">生成视频 ({{ formConfig.coin }}积分/个)</GradientButton>
                </t-tooltip>
            </div>
        </div>
    </div>

    <!-- 视频操作 -->
    <VideoOperations v-if="currentScriptData" v-bind="currentScriptData" @close="currentScriptData = null" @confirm="handleVideoOperationsConfirm" />

    <!-- 贴片操作 -->
    <WidgetOperations v-if="showWidgetEdit" :asides="formData.promptMap.asides" :widget="globalWidget" @close="showWidgetEdit = false" @confirm="handleWidgetConfirm" />
</template>

<script lang="ts" setup>
import { ref, computed, watch, inject, ComputedRef } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
import { RollbackIcon, InfoCircleFilledIcon } from "tdesign-icons-vue-next";

import { useUserStore } from "@/store/user";
import { rotateMaterial } from "@/api/material";
import { useCurrentScriptVideo } from "../common/hooks/useCurrentScriptVideo";
import { getOriginalVideoClip, transformSubtitlesToScript } from "../common/utils";
import { backToSubtitleEdit } from "../common/scripts";
import { VideoMaterialScript, FormConfig, VideoMaterial, PreviewMode } from "../common";
import type { UseCurrentScriptVideoProps, VideoOperationsResult, WidgetData } from "#/generate";

import SelectDirectoryCascader from "@/components/SelectDirectory/cascader.vue";
import GradientButton from "@/components/GradientButton/index.vue";
import VideoOperations from "./VideoOperations.vue";
import WidgetOperations from "./WidgetOperations/index.vue";
import StoryItem from "./StoryItem.vue";
import PatchItem from "./PatchItem.vue";
import WidgetItem from "./WidgetItem.vue";

interface Props extends UseCurrentScriptVideoProps {
    // 是否正在生成
    loading: boolean;
    // 预览模式
    previewMode?: PreviewMode;
    // 是否为视频或案例预览模式
    isPreview: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    digitalHumanConfigs: () => [],
    isOriginalVideo: false,
    isSpeechVideo: false,
});

const emit = defineEmits<{
    (e: "confirm", value: { videoCategoryPath: string; generateVideoCount: number }): void;
    (e: "change"): void;
    (e: "back"): void;
    (e: "clone"): void;
    (e: "editSubtitle"): void;
}>();

const userStore = useUserStore();

// 选中的分镜索引
const activeScriptIndex = defineModel<number>("activeScriptIndex", { required: true });

// 监听选中的分镜索引，然后进行滚动
watch(activeScriptIndex, (index) => {
    if (index === -1) {
        return;
    }

    // 获取内容容器和选中的分镜元素
    const parentEl: HTMLElement = document.querySelector(".content-wrapper")!;
    const childEl: HTMLElement = document.querySelector(`#story-item-${index}`)!;

    // 计算滚动位置
    const x = childEl.offsetLeft - parentEl.clientWidth / 2 + childEl.clientWidth / 2;

    // 平滑滚动到目标位置
    parentEl.scrollTo({ left: x, behavior: "smooth" });
});

// 是否显示两行素材
const twoLineMode = computed(() => !props.isSpeechVideo || Boolean(props.formData.promptMap.categoryIds.length));

// 已经选用的素材ids
const selectedMaterialIds = computed(()=>{
    console.log(scriptsModelValue)
    return scriptsModelValue.value.map((v) => v.materialList[0].materialId);
});

// 脚本列表
const scriptsModelValue = defineModel<VideoMaterialScript[]>("scripts", { required: true });

// 字幕位置Y轴偏移
const subtitlePositionY = defineModel<number>("subtitlePositionY", { required: true });

// 分镜下的字幕
const scriptSubtitles = transformSubtitlesToScript(computed(() => props.subtitles));

// 当前编辑的脚本数据
const { currentScriptData, handleEditMaterial, handleEditPatch, videoOperationsConfirm } = useCurrentScriptVideo({ props });

// 前贴片脚本
const beforeScriptModelValue = defineModel<VideoMaterialScript>("beforeScript", { required: true });
// 后贴片脚本
const afterScriptModelValue = defineModel<VideoMaterialScript>("afterScript", { required: true });

// 删除前后贴
const handleDeletePatch = (index: number) => {
    if (index === -99999) {
        beforeScriptModelValue.value.materialList = [];
    } else {
        afterScriptModelValue.value.materialList = [];
    }

    emit("change");
};

// 全局贴片
const widgetsModelValue = defineModel<WidgetData[]>("widgets", { required: true });

// 显示的贴片编辑框
const showWidgetEdit = ref(false);

// 当前贴片数据
const globalWidget = computed(() => widgetsModelValue.value?.[0]);

// 贴片操作确认事件
const handleWidgetConfirm = (value: WidgetData) => {
    widgetsModelValue.value = [value];
    emit("change");
};

// 删除贴片
const handleDeleteWidget = () => {
    widgetsModelValue.value = [];
    emit("change");
};

// 旋转中列表
const rotateingList = ref(new Set<string>());

// 旋转素材
const handleRotate = async (scriptIndex: number) => {
    let script = scriptsModelValue.value[scriptIndex];

    let item = script.materialList[0];

    // 全屏状态下
    if (script.digitalPersonDisplay === 2) {
        // 如果是口播
        if (props.isSpeechVideo) {
            item = script.sceneMaterialList[0];
        }
    }

    let rotate = ((item.rotate || 0) + 90) % 360;

    // 添加到旋转中列表
    rotateingList.value.add(item.materialId);

    let [res] = await rotateMaterial([{ materialId: item.materialId, rotate, materialType: item.materialType }]);

    // 从旋转中列表删除
    rotateingList.value.delete(item.materialId);

    if (res.errMsg) {
        MessagePlugin.error(res.errMsg);
        return;
    }

    let rotateFn = (v: VideoMaterial) => {
        if (v.materialId !== item.materialId) {
            return;
        }

        v.rotate = rotate;
    };

    scriptsModelValue.value.forEach((script) => {
        script.materialList.forEach(rotateFn);
        script.materialOriginList.forEach(rotateFn);
        script.sceneMaterialList.forEach(rotateFn);
        script.sceneMaterialOriginList.forEach(rotateFn);
    });
};

// 检查是否缺少素材
const checkMaterialNotEnough = (item: VideoMaterialScript) => {
    // 口播视频
    if (props.isSpeechVideo) {
        return !item.sceneMaterialList.length;
    }

    // 数字人
    if (!props.isOriginalVideo && !props.isSpeechVideo && props.formData.promptMap.digitalPersonOpen && item.digitalPersonDisplay === 2) {
        return false;
    }

    // 实拍视频
    if (props.isOriginalVideo) {
        // 获取实拍视频片段
        return getOriginalVideoClip(item.start, item.end, props.formData.promptMap.originalVideo).length < 1;
    }

    // 普通空镜素材
    return item.materialList.length < 1;
};

// 缺少素材的分镜
const missingMaterialScripts = computed(() => scriptsModelValue.value.map((v, i) => (checkMaterialNotEnough(v) ? i : undefined)).filter((v) => v !== undefined));

// 选择的目录ID
const videoCategoryId = defineModel<string>("videoCategoryId", { default: "" });
// 选择的目录路径
const videoCategoryPath = ref("默认目录");

// 生成视频数量
const generateVideoCount = ref(1);

// 禁用创作按钮
const gradientButtonDisabled = computed(() => {
    // 积分不足
    const coinNotEnough = userStore.coin < generateVideoCount.value * formConfig.value.coin;

    // 素材不足
    const materialNotEnough = missingMaterialScripts.value.length > 0;

    // 有场景描述为空
    const sceneEmpty = scriptsModelValue.value.some((v) => !v.scene);

    return coinNotEnough || materialNotEnough || sceneEmpty;
});

// 视频操作事件
const handleVideoOperation = (item: VideoMaterialScript, scriptIndex: number, colIndex: number, showTransition = false) => {
    handleEditMaterial(item, scriptIndex, colIndex, showTransition);

    currentScriptData?.value && (currentScriptData.value.excludedIds = selectedMaterialIds.value);
};

// 视频操作确认事件
const handleVideoOperationsConfirm = (e: VideoOperationsResult) => {
    const { scriptIndex, isPatch } = currentScriptData.value!;

    // 如果更新前后贴视频
    if (isPatch) {
        if (scriptIndex === -99999) {
            beforeScriptModelValue.value.materialList = e.patchVideos;
        } else {
            afterScriptModelValue.value.materialList = e.patchVideos;
        }

        return emit("change");
    }

    // 进行更新操作
    videoOperationsConfirm(e);

    emit("change");
};

const formConfig = inject<ComputedRef<FormConfig>>("formConfig")!;
</script>

<style scoped lang="less">
.generate-wrapper {
    > .content-wrapper {
        // 子节点的offsetLeft是根据最近的定位元素计算的，所以需要设置相对定位
        position: relative;
        display: flex;
        gap: 20px;
        overflow-x: overlay;
        padding-bottom: 0.5%;

        &::-webkit-scrollbar {
            height: 18px;

            &-thumb {
                visibility: visible;
            }
        }
    }
}
</style>
