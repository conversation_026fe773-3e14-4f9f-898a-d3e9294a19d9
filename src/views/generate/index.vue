<template>
    <div class="generate-page" ref="generatePageRef">
        <div class="generate-form-wrapper" :class="{ disabled: disabledForm, 'disabled-all-form-options': disabledAllFormEdit, collapsed: !formExpand && useNewVersion }">
            <!-- 展开收起 -->
            <span v-if="useNewVersion && composeTableVisible" class="yd-icon expand-btn" :class="formExpand ? 'YD-shouqi' : 'YD-zhankai'" @click="formExpand = !formExpand"></span>

            <div class="generate-form-header">
                <div class="generate-form-header-title" v-if="useNewVersion && composeTableVisible && !isPreview && !isFission">
                    <t-radio-group variant="primary-filled" v-model="formTab" class="full-width-radio-group" style="width: calc(100% - 30px)">
                        <t-radio-button value="subTitle">视频字幕</t-radio-button>
                        <t-radio-button value="generateForm">创作信息</t-radio-button>
                    </t-radio-group>
                </div>
                <div class="generate-form-header-title" v-else>创作配置</div>

                <!-- 表单tab -->
                <div class="generate-form-tabs" v-if="showStoryboardScriptButton || isPreview">
                    <t-button
                        class="tab-item"
                        v-for="item in videoTypeList"
                        :key="item.value"
                        :variant="promptMap.videoType === item.value ? 'outline' : 'base'"
                        shape="round"
                        size="medium"
                        :theme="promptMap.videoType === item.value ? 'primary' : 'default'"
                        @click="handleSwitchVideoType(item.value)"
                    >
                        {{ item.label }}
                    </t-button>
                </div>
            </div>

            <!-- 创作表单 -->
            <div class="generate-form-body" v-if="formTab === 'generateForm'">
                <!-- 口播视频 -->
                <FormCardVideo
                    v-if="[2, 3, 4].includes(promptMap.videoType)"
                    :title="`${videoTypeTitle}素材`"
                    :disabled="disablePartialFormEdit"
                    :icon="formIconOriginalVideo"
                    :list="oralVideoMaterialList"
                    @add="materialSelectorVisible = promptMap.videoType === 2 ? 'originalVideo' : 'oralVideoMaterial'"
                    @delete="oralVideoMaterialList.splice($event, 1)"
                >
                    <template #actions>
                        <t-button theme="primary" @click="handleGetRealCaptions" :loading="getRealCaptionsLoading" :disabled="oralVideoMaterialList.length === 0">提取视频文案</t-button>
                    </template>
                </FormCardVideo>

                <!-- 视频字幕 -->
                <FormCard
                    class="generate-form-card asides-wrapper"
                    :class="{ 'has-audio': promptMap.videoType === 1 && promptMap.audioFile, 'has-quick-actions': promptMap.videoType !== 1 }"
                    :disabled="disablePartialFormEdit"
                >
                    <template #title>
                        <div class="generate-form-title"><img src="@/assets/images/icon/form-icon-captions.png" />{{ videoTypeTitle }}字幕</div>
                    </template>

                    <template #actions>
                        <t-space size="small">
                            <t-button v-if="promptMap.videoType === 1" theme="primary" :loading="parseAudioing" @click="handleChooseAudio">上传音频</t-button>
                            <t-button theme="primary" @click="scriptLibraryVisible = true">从脚本库选择</t-button>
                        </t-space>
                    </template>

                    <div class="audio-wrapper" v-if="promptMap.audioFile">
                        <div class="instance">
                            <span class="yd-icon YD-icon-24-luyin"></span>

                            <div class="title">{{ promptMap.audioFileName }}</div>

                            <div class="btns">
                                <PlayCircleStrokeIcon v-if="!playState" @click="togglePlay(promptMap.audioFile)" />
                                <PauseCircleStrokeIcon v-else @click="togglePlay(promptMap.audioFile)" />

                                <DeleteIcon @click="handleDeleteAudio" />
                            </div>
                        </div>
                    </div>

                    <t-textarea
                        v-model="promptMap.asides"
                        :disabled="AiRepairCaptionsLoading !== null || parseAudioing"
                        placeholder="请输入描述文案"
                        :maxlength="800"
                        :autosize="{ minRows: 5, maxRows: 15 }"
                        @blur="getSubtitleContent"
                    />

                    <div class="quick-actions" v-if="promptMap.videoType !== 1">
                        <t-button
                            v-for="item in formConfig.subtitleRepairTags"
                            :key="item.value"
                            variant="outline"
                            ghost
                            size="small"
                            @click="handleGetAiRepairCaptions(item.value)"
                            :loading="AiRepairCaptionsLoading === item.value"
                            :disabled="promptMap.asides.length === 0 || AiRepairCaptionsLoading !== null"
                        >
                            {{ item.title }}
                        </t-button>
                    </div>
                </FormCard>

                <!-- 空镜素材 -->
                <FormCard class="generate-form-card">
                    <template #title>
                        <div class="generate-form-title"><img src="@/assets/images/icon/form-icon-material.png" />空镜素材</div>
                    </template>
                    <template #actions>
                        <t-button theme="primary" @click="tagSelectorVisible = 'categoryIds'">
                            <template #icon><AddIcon /></template>
                            选择空镜素材
                        </t-button>
                    </template>

                    <div class="box-panel tagBox" placeholder="请选择空镜素材">
                        <t-tag theme="default" closable size="large" :max-width="100" v-for="(tag, index) in promptMap.categoryIds" :key="tag.value" @close="handleDeleteSceneMaterial(index)">
                            {{ tag.title }}
                        </t-tag>
                    </div>
                </FormCard>

                <!-- 字幕风格 -->
                <FormCard v-if="!isFission" class="generate-form-card subtitle-tag-style">
                    <template #title>
                        <div class="generate-form-title"><img :src="formIconText" />字幕风格</div>
                    </template>

                    <template #actions>
                        <div class="tip" v-if="formConfig.subtitleTagStyleList?.length > 5" @click="subtitleTagStyleVisible = !subtitleTagStyleVisible">点击{{ subtitleTagStyleVisible ? "收起" : "更多" }}</div>
                    </template>

                    <div class="body">
                        <div
                            class="item"
                            :class="{ selected: promptMap.subtitleTagStyle === item.value }"
                            v-for="item in formConfig.subtitleTagStyleList?.filter?.((_, i) => i < 5 || subtitleTagStyleVisible)"
                            :key="item.value"
                            @click="handleFormChange('subtitleTagStyle', item)"
                        >
                            <img :src="item.icon" />
                            <div class="title">{{ item.title }}</div>
                        </div>
                    </div>
                </FormCard>

                <!-- 字幕样式 -->
                <FormCardCell v-if="!isFission" class="generate-form-card" title="字幕样式" :icon="formIconText" @click="captionSelectorVisible = true">
                    <template v-if="promptMap.subtitleOff"> 不显示字幕 </template>

                    <template v-else-if="promptMap.subtitleTextStyle">
                        {{ promptMap.subtitleFontName }}｜{{ promptMap.subtitleFontSize }}px｜<t-avatar :image="subtitleTextStyleIcon" size="small" style="margin: 0 6px; vertical-align: top" />
                    </template>
                </FormCardCell>

                <!-- 数字人/配音 -->
                <template v-if="isFission && ![2, 3, 4].includes(promptMap.videoType)">
                    <div class="generate-form-label-item">
                        <div class="header">
                            <label class="title">使用数字人</label>
                            <t-switch size="large" v-model="promptMap.digitalPersonOpen" @change="chooseDigitalHumanConfigs = []" />
                        </div>
                    </div>

                    <FormCardCell v-if="promptMap.digitalPersonOpen" class="generate-form-card" title="数字人" :icon="formIconMetaHuman" @click="metaHumanSelectorForFissionVisible = true">
                        <template v-if="chooseDigitalHumanConfigs.length">已选 {{ chooseDigitalHumanConfigs.length }} 个</template>
                        <template v-else>系统智能匹配</template>
                    </FormCardCell>

                    <FormCardCell v-else class="generate-form-card" title="配音" :icon="formIconAudio" @click="metaHumanSelectorForFissionVisible = true">
                        <template v-if="!promptMap.audioOpen">关闭配音</template>
                        <template class="digital-human-list" v-else-if="chooseDigitalHumanConfigs.length">已选 {{ chooseDigitalHumanConfigs.length }} 个</template>
                        <template v-else>系统智能匹配</template>
                    </FormCardCell>
                </template>

                <!-- 背景音乐 -->
                <FormCardCell class="generate-form-card" title="音乐" :icon="formIconMusic" @click="musicSelectorVisible = true" :value="backgroundMusicText"></FormCardCell>

                <!-- 实拍设置 -->
                <FormCard v-if="[2, 3, 4].includes(promptMap.videoType) && !isFission" class="generate-form-card" :disabled="disablePartialFormEdit">
                    <template #title>
                        <div class="generate-form-title"><img src="@/assets/images/icon/form-icon-captions.png" />口播设置</div>
                    </template>

                    <t-radio-group variant="primary-filled" v-model="promptMap.sceneVideoDisplay" class="full-width-radio-group">
                        <t-radio-button :value="2">全屏+空镜</t-radio-button>
                        <t-radio-button :value="3">浮屏+空镜</t-radio-button>
                        <t-radio-button :value="4">仅口播视频</t-radio-button>
                    </t-radio-group>
                </FormCard>

                <!-- <FormCard class="generate-form-card"> -->
                <!-- <template #title> -->
                <!-- <div class="generate-form-title"><img src="@/assets/images/icon/form-icon-materialMatchAlgorithm.png" />素材匹配算法</div> -->
                <!-- </template> -->
                <!--  -->
                <!-- <t-radio-group variant="primary-filled" v-model="promptMap.difyWeave" class="full-width-radio-group"> -->
                <!-- <t-radio-button :value="false">默认</t-radio-button> -->
                <!-- <t-radio-button :value="true">优选（时间久）</t-radio-button> -->
                <!-- </t-radio-group> -->
                <!-- </FormCard> -->

                <!-- 分镜智能转场 -->
                <FormCard v-if="!isFission" class="generate-form-card" :disabled="disablePartialFormEdit">
                    <template #title>
                        <div class="generate-form-title"><img src="@/assets/images/icon/form-icon-LUT.png" />分镜转场</div>
                    </template>

                    <t-radio-group variant="primary-filled" v-model="promptMap.transitionType" class="full-width-radio-group">
                        <t-radio-button :value="1">智能转场</t-radio-button>
                        <t-radio-button :value="0">人工配置</t-radio-button>
                    </t-radio-group>
                </FormCard>

                <!-- LUT设置 -->
                <FormCard v-if="!isFission" class="generate-form-card">
                    <template #title>
                        <div class="generate-form-title"><img src="@/assets/images/icon/form-icon-LUT.png" />LUT设置</div>
                    </template>

                    <t-radio-group variant="primary-filled" v-model="promptMap.videoLut" class="full-width-radio-group">
                        <t-radio-button :value="item.value" v-for="item in formConfig.lutList" :key="item.value">{{ item.title }}</t-radio-button>
                    </t-radio-group>
                </FormCard>

                <!-- 返回修改配置按钮 -->
                <t-button
                    v-if="disabledForm"
                    size="large"
                    theme="default"
                    style="position: sticky; left: 50%; bottom: 30px; z-index: 2; transform: translateX(-50%)"
                    @click="handleBackChange"
                    :disabled="storyboardScriptLoading"
                >
                    <template #icon><RollbackIcon /></template>
                    返回修改配置
                </t-button>

                <!-- 生成分镜脚本按钮 -->
                <GradientButton v-else-if="showStoryboardScriptButton" :loading="submiting" block shape="round" size="large" class="generate-form-submit-button" @click="handleSubmit">生成分镜脚本</GradientButton>
            </div>

            <!-- 字幕打标高亮 -->
            <SubtitleHighlight
                class="subtitle-highlight"
                v-else-if="formTab === 'subTitle'"
                ref="subtitleHighlightRef"
                v-model:activeScriptIndex="activeScriptIndex"
                v-model:subtitleHighlightVersion="promptMap.subtitleHighlightVersion"
                v-model:subtitles="subtitles"
                @change="updateProjectDraft"
            />
        </div>

        <div class="generate-table-wrapper" :class="{ 'generate-table-wrapper-float': storyboardScriptVisible && promptMap.videoType !== 1 }">
            <!-- 生成脚本中 -->
            <div class="generate-loading" v-if="submiting"><Loading text="正在为你生成分镜脚本，预计需要{{time}}秒" /></div>

            <!-- 字幕编排 -->
            <ComposeSubtitle
                v-else-if="storyboardScriptVisible"
                v-bind="promptMap"
                v-model:subtitles="subtitles"
                v-model:digitalHumanConfigs="digitalHumanConfigs"
                v-model:digitalPersonSwitch="promptMap.digitalPersonSwitch"
                v-model:audioOpen="promptMap.audioOpen"
                v-model:digitalPersonOpen="promptMap.digitalPersonOpen"
                :loading="storyboardScriptLoading"
                :needUpdateHuman="storyboardScriptVisible === 1"
                :isOriginalVideo="isOriginalVideo"
                :isSpeechVideo="isSpeechVideo"
                @confirm="handleStoryboardScriptConfirm('normal')"
                @updateHuman="handleStoryboardScriptConfirm('updateHuman')"
            />

            <!-- 合成表 -->
            <template v-else-if="composeTableVisible">
                <StoryWall
                    v-if="useNewVersion && !isFission"
                    v-model:scripts="scripts"
                    v-model:videoCategoryId="videoCategoryId"
                    v-model:subtitlePositionY="promptMap.subtitlePositionY"
                    v-model:beforeScript="beforeScript"
                    v-model:afterScript="afterScript"
                    v-model:activeScriptIndex="activeScriptIndex"
                    v-model:widgets="widgets"
                    :formData="formData"
                    :subtitles="subtitles"
                    :digitalHumanConfigs="digitalHumanConfigs"
                    :loading="composeTableConfirming"
                    :previewMode="previewMode"
                    :isPreview="isPreview"
                    :isOriginalVideo="isOriginalVideo"
                    :isSpeechVideo="isSpeechVideo"
                    @back="switchStage('subtitle')"
                    @editSubtitle="handleEditSubtitle"
                    @change="updateProjectDraft"
                    @confirm="handleComposeTableConfirm"
                    @clone="handleComposeTableClone"
                />

                <ComposeTable
                    v-else
                    v-model:scripts="scripts"
                    v-model:videoCategoryId="videoCategoryId"
                    v-model:beforeScript="beforeScript"
                    v-model:afterScript="afterScript"
                    v-model:mashUpType="mashUpType"
                    v-model:noDuplicatePercent="noDuplicatePercent"
                    v-model:widgets="widgets"
                    :formData="formData"
                    :subtitles="subtitles"
                    :digitalHumanConfigs="digitalHumanConfigs"
                    :chooseDigitalHumanConfigs="chooseDigitalHumanConfigs"
                    :loading="composeTableConfirming"
                    :previewMode="previewMode"
                    :isPreview="isPreview"
                    :isFission="isFission"
                    :isOriginalVideo="isOriginalVideo"
                    :isSpeechVideo="isSpeechVideo"
                    @back="switchStage('subtitle')"
                    @change="updateProjectDraft"
                    @confirm="handleComposeTableConfirm"
                    @clone="handleComposeTableClone"
                />
            </template>

            <!-- 引导图 -->
            <img v-else class="guide-image" src="@/assets/images/generate/guide.png" />
        </div>

        <!-- 素材目录选择 -->
        <FolderSelector
            v-if="tagSelectorVisible"
            :value="promptMap.categoryIds"
            :vertical="promptMap.vertical"
            :originAudio="promptMap.originAudio"
            storageKey="sceneMaterialTagHistory"
            @close="tagSelectorVisible = false"
            @confirm="handleFormChange('categoryIds', $event)"
        />

        <!-- 视频素材选择器 -->
        <MaterialSelector v-if="materialSelectorVisible" v-model:visible="materialSelectorVisible" :baseRequestParams="{ videoType: 2 }" :list="oralVideoMaterialList" @confirm="handleSelectMaterial" />

        <!-- 脚本创作 -->
        <ScriptEditor v-if="scriptEditorVisible" v-model:visible="scriptEditorVisible" />

        <!-- 脚本库 -->
        <ScriptLibrary v-if="scriptLibraryVisible" v-model:visible="scriptLibraryVisible" @confirm="handleScriptLibrary" />

        <!-- 字幕选择器  -->
        <CaptionSelector v-if="captionSelectorVisible" v-model:visible="captionSelectorVisible" v-bind="promptMap" @confirm="handleFormChange('subtitleTextStyle', $event)" />

        <!-- 音乐选择器 -->
        <MusicSelector
            v-if="musicSelectorVisible"
            v-model:visible="musicSelectorVisible"
            v-bind="promptMap"
            :isFission="isFission"
            @select="updateEchoData('music', $event)"
            @confirm="handleFormChange('chooseMusic', $event)"
        />

        <!-- 裂变数字人 -->
        <MetaHumanSelectorForFission
            v-if="metaHumanSelectorForFissionVisible"
            v-model:visible="metaHumanSelectorForFissionVisible"
            v-bind="promptMap"
            v-model:chooseDigitalHumanConfigs="chooseDigitalHumanConfigs"
            v-model:digitalPersonSwitch="promptMap.digitalPersonSwitch"
            v-model:digitalPersonOpen="promptMap.digitalPersonOpen"
            v-model:audioOpen="promptMap.audioOpen"
            :isFission="isFission"
            @confirm="handleFormChange('chooseDigitalHumanConfigs', $event)"
        />
    </div>

    <t-switch
        v-if="composeTableVisible && ['development', 'test'].includes(appStore.ENV_MODE)"
        class="toogle-btn"
        v-model="useNewVersion"
        size="large"
        :label="['故事板', '分镜表']"
        @change="switchStage('composeTable')"
    ></t-switch>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, provide, readonly, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { MessagePlugin, DialogPlugin } from "tdesign-vue-next";
import { AddIcon, DeleteIcon, RollbackIcon, PlayCircleStrokeIcon, PauseCircleStrokeIcon } from "tdesign-icons-vue-next";
import { cloneDeep } from "lodash";

import { useAppStore } from "@/store/app";
import { useUserStore } from "@/store/user";
import { getVideoDetail } from "@/api/video";
import {
    getFormConfig,
    getProjectDraft,
    formatSubtitleContent,
    generateStoryboard,
    matchScene,
    updateSceneHuman,
    generateVideo,
    generateFissionVideo,
    saveProjectDraft,
    cloneProject,
    getRealCaptions,
    getAiRepairCaptions,
} from "@/api/generate";
import { Folder, WidgetData } from "#/generate";
import Upload from "@/utils/upload";
import { useMusic } from "@/hooks/useMusic";
import { log2java } from "@/common/log";
import { sleep, debounce } from "@/utils/fn";

import { FormConfig, PromptMap, DigitalHuman, Subtitle, VideoMaterialScript, PreviewMode } from "./common";
import { transformDigitalHumanConfigs, transformSubtitles, generatePatchScript } from "./common/utils";
import { createVideoGenerateConfirmDialog } from "./common/scripts";
import { useEchoData } from "./common/hooks/useEchoData";
import { videoTypeList } from "./common/fields";

import GradientButton from "@/components/GradientButton/index.vue";
import Loading from "@/components/Loading/index.vue";

import FormCard from "./components/FormCard/Index.vue";
import FormCardCell from "./components/FormCard/Cell.vue";
import FormCardVideo from "./components/FormCard/Video.vue";
import FolderSelector from "./components/Selector/Folder.vue";
import MaterialSelector from "./components/MaterialSelector/Index.vue";
import ScriptEditor from "./components/ScriptEditor.vue";
import ScriptLibrary from "./components/ScriptLibrary.vue";
import CaptionSelector from "./components/Selector/Caption.vue";
import MusicSelector from "./components/Selector/Music.vue";
import MetaHumanSelectorForFission from "./components/MetaHumanSelector/Fission.vue";

import ComposeSubtitle from "./components/ComposeSubtitle.vue";
import ComposeTable from "./components/ComposeTable.vue";
import StoryWall from "./components/StoryWall.vue";
import SubtitleHighlight from "./components/SubtitleHighlight.vue";

import formIconText from "@/assets/images/icon/form-icon-text.png";
import formIconMusic from "@/assets/images/icon/form-icon-music.png";
import formIconMetaHuman from "@/assets/images/icon/form-icon-metaHuman.png";
import formIconAudio from "@/assets/images/icon/form-icon-audio.png";
import formIconOriginalVideo from "@/assets/images/icon/form-icon-originalVideo.png";
import { getFolderList } from "@/api/folder";

// 获取路由
const router = useRouter();
// 获取路由参数
const route = useRoute();

const appStore = useAppStore();
const userStore = useUserStore();

// 提供 ref 给子组件
const generatePageRef = ref<HTMLElement | null>(null);
provide("generatePageRef", generatePageRef);

// 填充表单配置
const formConfig = ref<FormConfig>({} as FormConfig);
getFormConfig().then((res) => (formConfig.value = res));
provide("formConfig", readonly(formConfig));

const folderTree = ref<Folder[]>([]);
getFolderList().then((res) => (folderTree.value = res));
provide("folderTree", readonly(folderTree));

// 使用新版本
const useNewVersion = ref(true);

// 提交中
const submiting = ref(false);

// 控制标签选择器的可见性
const tagSelectorVisible = ref<"categoryIds" | false>(false);

// 控制素材选择器的可见性
const materialSelectorVisible = ref<"originalVideo" | "oralVideoMaterial" | false>(false);

// 控制脚本创作的可见性
const scriptEditorVisible = ref(false);

// 控制脚本库的可见性
const scriptLibraryVisible = ref(false);

// 控制字幕包装的可见性
const subtitleTagStyleVisible = ref(false);

// 控制字幕选择器的可见性
const captionSelectorVisible = ref(false);

// 控制音乐选择器的可见性
const musicSelectorVisible = ref(false);

// 显示裂变数字人列表
const metaHumanSelectorForFissionVisible = ref(false);

// 控制生成分镜脚本的可见性: 0=不可见, 1=字幕+更新数字人按钮, 2=仅字幕
const storyboardScriptVisible = ref<0 | 1 | 2>(0);

// 控制合成表的可见性
const composeTableVisible = ref(false);

// 选择素材
const handleSelectMaterial = (e: any) => {
    switch (materialSelectorVisible.value) {
        // 实拍视频
        case "originalVideo":
            handleFormChange("originalVideo", e);
            break;

        // 口播视频
        case "oralVideoMaterial":
            handleFormChange("oralVideoMaterial", e);
            break;
    }
};

// 选中的分镜索引
const activeScriptIndex = ref(-1);

// 切换阶段，type: form=表单阶段, composeTable=合成表阶段, subtitle=字幕阶段(有仅更新数字人), subtitleOnly=字幕阶段(仅字幕)
const switchStage = (type: "form" | "composeTable" | "subtitle" | "subtitleOnly") => {
    storyboardScriptVisible.value = { form: 0, composeTable: 0, subtitle: 1, subtitleOnly: 2 }[type] as typeof storyboardScriptVisible.value;
    composeTableVisible.value = type === "composeTable";
    formExpand.value = !composeTableVisible.value;
    disabledForm.value = !previewMode.value && type !== "form";

    if (useNewVersion.value && disabledForm.value && type === "composeTable") {
        disabledForm.value = false;
    }

    // 如果切换到合成表阶段，则重置选中的分镜索引
    if (composeTableVisible.value) {
        activeScriptIndex.value = -1;

        if (!isPreview.value && !isFission.value) {
            // 等待1秒后切换到字幕阶段, 解决切换阶段时，字幕内容没有更新导致报错和添加了textarea没有删除问题
            sleep(1).then(() => (formTab.value = "subTitle"));
        }
    } else {
        // 切换到表单阶段，则重置表单标签
        formTab.value = "generateForm";
    }
};

// 视频类型标题
const videoTypeTitle = computed(() => (({ 1: "视频", 2: "口播", 3: "口播", 4: "多人剧情" }) as any)[promptMap.videoType]);

// 是否为预览模式
const previewMode = ref<PreviewMode>();
// 是否为视频或案例预览模式
const isPreview = computed(() => ["video", "case"].includes(previewMode.value!));
// 是否为裂变混剪模式
const isFission = computed(() => previewMode.value === "fission");
// 是否为口播视频
const isSpeechVideo = computed(() => [3, 4].includes(promptMap.videoType));
// 是否为实拍视频
const isOriginalVideo = computed(() => promptMap.videoType === 2);

// 创作的时候需要传的各类ID
const formParamIds = reactive({
    // 视频任务ID
    taskId: "",
    // 表单草稿ID
    draftId: ((route.query.draftId || route.query.caseId) as string) || "0",
    // 失败的重试ID
    retryTaskId: "",
    // 任务ID
    creationTaskId: "",
});

// 提交的表单数据
const promptMap = reactive<PromptMap>({
    // 视频类型: 1=文案成片 2=素材成片
    videoType: 1,

    appId: 10116,

    // 上传的实拍视频
    originalVideo: [],
    // 上传的口播视频
    oralVideoMaterial: [],

    // 空镜素材列表
    categoryIds: [],
    // 脚本
    asides: "",
    // 字体
    subtitleFontName: "",
    // 字幕样式
    subtitleTextStyle: "",
    // 字体大小
    subtitleFontSize: 14,
    // 字幕位置Y轴偏移
    subtitlePositionY: -200,

    // 口播展示形式
    sceneVideoDisplay: 2,
    // 是否开启数字人
    digitalPersonOpen: false,
    // 数字人展示形式
    digitalPersonSwitch: 3,
    // 是否开启配音
    audioOpen: true,
    // 是否使用背景音乐
    bgmOpen: true,
    // 背景音乐类型
    bgmType: "",
    // 背景音乐ID
    chooseMusic: "",
    // 背景音乐ID列表（裂变专属）
    chooseMusicIds: [],
    // 背景音乐音量
    bgmVolume: 0,
    // 视频规格
    vertical: 3,
    // 画面匹配算法
    difyWeave: false,
    // 使用素材原声
    originAudio: false,
    // LUT设置
    videoLut: "",
    // 是否关闭字幕
    subtitleOff: false,
    // 字幕打标风格
    subtitleTagStyle: "1",
    // 字幕划重点版本
    subtitleHighlightVersion: null,

    // 音频文件
    audioFile: "",
    // 音频文件名称
    audioFileName: "",

    // 分镜智能转场
    transitionType: 0,

    // 从脚本库传过来的参数
    ...history.state?.promptMap,
});

// 视频目录ID
const videoCategoryId = ref("0");
// 裂变合成方式
const mashUpType = ref(2);
// 裂变重复率
const noDuplicatePercent = ref(0);

// 回显数据
const { getBackgroundMusic, initEchoData, updateEchoData } = useEchoData(promptMap);

// 背景音乐表单显示文本
const backgroundMusicText = computed(() => {
    if (!promptMap.bgmOpen) {
        return "无音乐";
    }

    // 裂变混剪
    if (isFission.value && promptMap.chooseMusicIds.length > 1) {
        return `已选 ${promptMap.chooseMusicIds.length} 首`;
    }

    return getBackgroundMusic(promptMap.chooseMusicIds[0]).fileName;
});

// 实拍/口播/情景剧素材列表
const oralVideoMaterialList = computed(() => (promptMap.videoType === 2 ? promptMap.originalVideo : promptMap.oralVideoMaterial));

// 数字人列表
const digitalHumanConfigs = ref<DigitalHuman[]>([]);
// 数字人列表(裂变专属)
const chooseDigitalHumanConfigs = ref<DigitalHuman[]>([]);

// 字幕脚本
const subtitles = ref<Subtitle[]>([]);

// 对话角色
const roles = ref<string[]>([]);

// 脚本
const scripts = ref<VideoMaterialScript[]>([]);

// 前贴片脚本
const beforeScript = ref(generatePatchScript());
// 后贴片脚本
const afterScript = ref(generatePatchScript());

// 全局贴片
const widgets = ref<WidgetData[]>([]);

// 匹配字幕样式图标
const subtitleTextStyleIcon = computed(() => formConfig.value?.subtitlesStyleList?.find((v) => v.id === promptMap.subtitleTextStyle)?.icon);

// 上次请求的数字人内容
let lastMetaHumanContent = "";

// 获取字幕内容
const getSubtitleContent = async () => {
    // 如果是在视频或案例预览，则不格式化
    if (isPreview.value || promptMap.videoType !== 1) {
        return;
    }

    // 如果内容没有变化 或 已经格式化过（一次创作只格式化一次）
    if (lastMetaHumanContent === promptMap.asides || lastMetaHumanContent) return;

    lastMetaHumanContent = promptMap.asides;

    let res = await formatSubtitleContent(promptMap.asides);

    // 如果有字幕的时候，则更新字幕
    if (res.formattedSubtitle) {
        promptMap.asides = res.formattedSubtitle;
    }
};

// 加载数据
const loadData = async (data: any, updateKeys?: ("taskId" | "draftId" | "subtitles" | "roles" | "scripts" | "digitalHumanConfigs")[]) => {
    const updateAll = !updateKeys;

    if (updateAll || updateKeys.includes("taskId")) formParamIds.taskId = data.taskId || "";
    if (updateAll || updateKeys.includes("draftId")) formParamIds.draftId = data.draftId || "";
    if (updateAll) formParamIds.retryTaskId = data.retryTaskId || "";
    if (updateAll) formParamIds.creationTaskId = data.creationTaskId || "";

    // 更新目录ID
    if (updateAll && data.videoCategoryId !== null) {
        videoCategoryId.value = data.videoCategoryId;
    }

    // 字幕
    if (updateAll || updateKeys.includes("subtitles")) {
        subtitles.value = transformSubtitles(data.subtitles);
    }

    // 角色
    if (updateAll || updateKeys.includes("roles")) {
        roles.value = data.roles || [];
    }

    // 脚本
    if (updateAll || updateKeys.includes("scripts")) {
        scripts.value = data.scripts || [];
    }

    // 裂变合成方式
    if (updateAll) {
        mashUpType.value = data.mashUpType ?? 2;
    }
    // 裂变重复率
    if (updateAll) {
        noDuplicatePercent.value = data.noDuplicatePercent ?? 0;
    }

    // 前后贴
    if (updateAll) {
        beforeScript.value = data.beforeScript || generatePatchScript();
        afterScript.value = data.afterScript || generatePatchScript();
    }

    // 数字人
    if (updateAll || updateKeys.includes("digitalHumanConfigs")) {
        transformDigitalHumanConfigs(data.digitalHumanConfigs).then((res) => (digitalHumanConfigs.value = res));
    }

    // 数字人(裂变)
    if (updateAll && isFission.value) {
        transformDigitalHumanConfigs(data.chooseDigitalHumanConfigs).then((res) => (chooseDigitalHumanConfigs.value = res));
    }

    // 全局贴片
    if (updateAll) {
        widgets.value = data.widgets || [];
    }

    // 如果不是裂变，就把数据转换一下。统一到chooseMusicIds使用，提交的时候再恢复原本的字段提交
    if (!isFission.value) {
        data.promptMap.chooseMusicIds = [data.promptMap.chooseMusic].filter(Boolean);
    }

    Object.assign(promptMap, { ...data.promptMap });

    // 初化回显数据
    initEchoData();
};

// 获取实拍字幕
const getRealCaptionsLoading = ref(false);
const handleGetRealCaptions = async () => {
    getRealCaptionsLoading.value = true;
    try {
        let res = await getRealCaptions(
            promptMap[promptMap.videoType === 2 ? "originalVideo" : "oralVideoMaterial"].map((v) => v.url),
            promptMap.videoType,
        );
        handleFormChange("asides", res.subtitle);
    } catch (error) {}
    getRealCaptionsLoading.value = false;
};

// 获取AI修复字幕
const AiRepairCaptionsLoading = ref<number | null>(null);
const handleGetAiRepairCaptions = async (value: number) => {
    AiRepairCaptionsLoading.value = value;
    try {
        let res = await getAiRepairCaptions(promptMap.asides, promptMap.videoType, value, oralVideoMaterialList.value.length);
        if (res.subtitle) {
            promptMap.asides = res.subtitle;
        }
    } catch (error) {}
    AiRepairCaptionsLoading.value = null;
};

// 删除空镜素材
const handleDeleteSceneMaterial = (index: number) => {
    promptMap.categoryIds.splice(index, 1);

    if (isFission.value) {
        updateProjectDraft();
    }
};

// 修改表单
const handleFormChange = (key: keyof typeof promptMap | "chooseDigitalHumanConfigs", e: any) => {
    // 根据不同类型，修改表单
    switch (key) {
        // 字幕风格
        case "subtitleTagStyle":
            promptMap.subtitleTagStyle = e.value;
            promptMap.subtitleFontName = e.textFont?.name ?? promptMap.subtitleFontName;
            promptMap.subtitleFontSize = e.fontSize ?? promptMap.subtitleFontSize;
            promptMap.subtitleTextStyle = e.subtitlesStyle?.id ?? promptMap.subtitleTextStyle;
            break;

        // 字幕选择器
        case "subtitleTextStyle":
            promptMap.subtitleFontName = e.subtitleFontName;
            promptMap.subtitleFontSize = e.subtitleFontSize;
            promptMap.subtitleTextStyle = e.subtitleTextStyle;
            promptMap.subtitleOff = e.subtitleOff;
            break;

        // 音乐选择器
        case "chooseMusic":
            promptMap.chooseMusicIds = e.chooseMusicIds;
            promptMap.bgmType = e.bgmType;
            promptMap.bgmOpen = e.bgmOpen;
            promptMap.bgmVolume = e.bgmVolume;

            if (isFission.value) {
                updateProjectDraft();
            }
            break;

        // 脚本
        case "asides":
            promptMap.asides = e;
            getSubtitleContent();
            break;

        // 选择素材
        case "categoryIds":
            promptMap[key] = e.value;
            promptMap.vertical = e.vertical;
            promptMap.originAudio = e.originAudio;

            if (isFission.value) {
                updateProjectDraft();
            }
            break;

        // 更新数字人(裂变专属)
        case "chooseDigitalHumanConfigs":
            promptMap.digitalPersonSwitch = e.digitalPersonSwitch;
            promptMap.digitalPersonOpen = e.digitalPersonOpen;
            promptMap.audioOpen = e.audioOpen;
            chooseDigitalHumanConfigs.value = e.chooseDigitalHumanConfigs;
            updateProjectDraft();
            break;

        // 默认是改单值的。统一走这里
        default:
            (promptMap[key] as any) = e;
            break;
    }
};

// 表单参数
const formData = computed(() =>
    cloneDeep({
        videoCategoryId: videoCategoryId.value,
        mashUpType: mashUpType.value,
        noDuplicatePercent: noDuplicatePercent.value,

        ...formParamIds,
        promptMap: {
            ...promptMap,
            chooseMusic: isFission.value ? "" : promptMap.chooseMusicIds[0],
            chooseMusicIds: isFission.value ? promptMap.chooseMusicIds : [],
        },
        scripts: scripts.value,
        subtitles: subtitles.value,
        digitalHumanConfigs: digitalHumanConfigs.value,
        chooseDigitalHumanConfigs: chooseDigitalHumanConfigs.value,
        roles: roles.value,
        beforeScript: beforeScript.value,
        afterScript: afterScript.value,
        widgets: widgets.value,
    }),
);

// 校验表单数据
const validateForm = () => {
    let errorMessage = "";

    // 为了优先判断文案成片的这个提示
    if (promptMap.videoType === 2 && !promptMap.originalVideo.length) {
        errorMessage = "请上传实拍视频";
    } else if (promptMap.videoType === 3 && !promptMap.oralVideoMaterial.length) {
        errorMessage = "请上传口播视频";
    } else if (promptMap.videoType === 4 && !promptMap.oralVideoMaterial.length) {
        errorMessage = "请上传情景剧视频";
    } else if (promptMap.asides.length < 15) {
        errorMessage = "视频字幕不能少于15字";
    }

    // 素材成片
    else if (promptMap.videoType === 2) {
        // todo
        // 文案成片
    } else if (promptMap.videoType === 1) {
        if (!promptMap.categoryIds.length) {
            errorMessage = "请选择空镜素材";
        }
    }

    if (errorMessage) {
        MessagePlugin.warning(errorMessage);
    }

    return errorMessage;
};

// 提交表单
let handleSubmit = async () => {
    let errorMessage = validateForm();

    // 创作表单-生成分镜
    log2java.CREATE_FORM_GENERATE_STORYBOARD({
        draftId: formParamIds.draftId,
        errMsg: errorMessage,
        asides: promptMap.asides,
    });

    if (errorMessage) return;

    submiting.value = true;

    try {
        // 生成分镜的时候，清空脚本
        scripts.value = [];

        let res = await generateStoryboard(formData.value);

        // 创作表单-生成分镜成功
        log2java.CREATE_FORM_GENERATE_STORYBOARD_SUCCESS({ draftId: res.draftId, asides: promptMap.asides });

        router.replace({ path: route.path, query: { draftId: res.draftId } });

        // 清空历史记录
        history.replaceState(null, "");

        await loadData(res, ["taskId", "draftId", "subtitles", "roles", "digitalHumanConfigs"]);

        switchStage("subtitleOnly");
    } catch (error) {
        // 创作表单-生成分镜失败
        log2java.CREATE_FORM_GENERATE_STORYBOARD_FAIL({
            draftId: formParamIds.draftId,
            errMsg: error,
            asides: promptMap.asides,
        });
    }

    submiting.value = false;
};

// 确认字幕脚本
const storyboardScriptLoading = ref(false);

// 确认字幕脚本
const handleStoryboardScriptConfirm = async (matchType: "normal" | "updateHuman") => {
    storyboardScriptLoading.value = true;

    // 字幕编排-匹配画面
    log2java.CREATE_FORM_MATCH_SCREEN({ draftId: formParamIds.draftId, matchType });

    try {
        if (matchType === "normal") {
            // 生成字幕的时候，清空脚本
            scripts.value = [];
        }

        // 清掉字幕的wordAta，后端接口不处理，放到了前端处理
        let res = await (matchType === "normal" ? matchScene : updateSceneHuman)({ ...formData.value, subtitles: subtitles.value.map(({ wordAta, ...rest }) => rest) as Subtitle[] });

        await loadData(res, ["subtitles", "scripts"]);

        switchStage("composeTable");

        // 字幕编排-匹配画面成功
        log2java.CREATE_FORM_MATCH_SCREEN_SUCCESS({ draftId: formParamIds.draftId, matchType });
    } catch (error) {
        // 字幕编排-匹配画面失败
        log2java.CREATE_FORM_MATCH_SCREEN_FAIL({ draftId: formParamIds.draftId, matchType, errMsg: error });
    }

    storyboardScriptLoading.value = false;
};

// 更新项目草稿
const updateProjectDraft = () => {
    if (isPreview.value) {
        return;
    }

    saveProjectDraft(formData.value);
};

// 表格生成中
const composeTableConfirming = ref(false);

// 确认合成表
const handleComposeTableConfirm = async (e: { videoCategoryPath: string; generateVideoCount: number }) => {
    if (debounce("handleComposeTableConfirm", 1)) {
        return;
    }

    // 裂变时，视频目录为必选
    if (isFission.value && !videoCategoryId.value) {
        return MessagePlugin.warning("请选择视频目录");
    }

    // 创作表单-生成视频
    log2java.CREATE_FORM_GENERATE_VIDEO({ draftId: formParamIds.draftId });

    composeTableConfirming.value = true;

    if (isFission.value) {
        // 裂变混剪
        await generateFissionVideo({ ...formData.value, count: e.generateVideoCount }).finally(() => (composeTableConfirming.value = false));
    } else {
        // 脚本创作
        await generateVideo(formData.value).finally(() => (composeTableConfirming.value = false));
    }

    // 获取用户信息（更新积分）
    userStore.GET_USER_INFO();

    let code = await createVideoGenerateConfirmDialog({ ...e, time: isFission.value ? 0.5 : 1 });

    if (code === "list") {
        router.replace("/video");
    } else {
        MessagePlugin.success("创作成功");
        await router.replace({ query: {} });
        window.location.reload();
    }
};

// 使用分镜
const handleComposeTableClone = async () => {
    if (!isPreview.value) {
        return;
    }

    submiting.value = true;
    let { projectContent } = await cloneProject(previewMode.value === "video" ? { taskId: formParamIds.taskId, type: 2 } : { draftId: formParamIds.draftId, type: 2 }).finally(() => (submiting.value = false));

    router.replace({ query: { draftId: projectContent.draftId } });
    previewMode.value = undefined;

    await loadData(projectContent);

    if (scripts.value.length) {
        switchStage("composeTable");
    }
};

// 表单展开
const formExpand = ref(true);
const formTab = ref<"generateForm" | "subTitle">("generateForm");
const subtitleHighlightRef = ref<InstanceType<typeof SubtitleHighlight>>();

// 编辑字幕
const handleEditSubtitle = () => {
    formExpand.value = true;
    formTab.value = "subTitle";
    subtitleHighlightRef.value?.toggleMarkImportantEnabled(true);
};

// 切换创作类型
const handleSwitchVideoType = (type: typeof promptMap.videoType) => {
    promptMap.videoType = type;

    // 切换的时候，删除音频
    handleDeleteAudio();
};

const { playState, togglePlay, pause } = useMusic();

// 音频状态
const parseAudioing = ref(false);
// 选择音频
const handleChooseAudio = async () => {
    try {
        let { url, name } = await Upload.audio("audioManagement", { onInput: () => (parseAudioing.value = true) });
        let res = await getRealCaptions([url], promptMap.videoType);

        if (!res.subtitle.length) {
            MessagePlugin.warning("识别不到文字，请重新上传音频！");
            return;
        }

        handleFormChange("asides", res.subtitle);

        promptMap.audioFile = url;
        promptMap.audioFileName = name;
    } finally {
        parseAudioing.value = false;
    }
};
// 删除音频
const handleDeleteAudio = () => {
    promptMap.audioFile = "";
    promptMap.audioFileName = "";
    parseAudioing.value = false;
    pause();
};

// 使用脚本库脚本填充
const handleScriptLibrary = (e: string) => {
    handleFormChange("asides", e);
    handleDeleteAudio();
};

// 是否禁用表单
const disabledForm = ref(false);
// 返回修改配置
const handleBackChange = () => {
    const confirmDia = DialogPlugin({
        header: "是否确认修改配置",
        body: "修改配置将清空分镜配置，是否确认操作？",
        placement: "center",
        destroyOnClose: true,
        closeOnOverlayClick: false,
        onConfirm: async () => {
            switchStage("form");

            confirmDia.hide();
        },
        onClose: () => {
            confirmDia.hide();
        },
    });
};

// 是否显示生成分镜脚本按钮
const showStoryboardScriptButton = computed(() => {
    // 如果表单被禁用，或者分镜大纲被打开，则不显示生成分镜脚本按钮
    if (composeTableVisible.value && useNewVersion.value) {
        return false;
    }

    // 视频预览 和 案例预览 不显示生成分镜脚本按钮
    return !isPreview.value;
});

// 禁用部分表单编辑
const disablePartialFormEdit = computed(() => !disabledForm.value && composeTableVisible.value && useNewVersion.value);

// 禁用全部表单
const disabledAllFormEdit = computed(() => !disabledForm.value && isPreview.value);

onMounted(async () => {
    switch (history.state.projectType) {
        // 裂变混剪
        case 1:
            previewMode.value = "fission";
            break;

        // 脚本创作
        case 2:

        // 其他默认场景
        default:
            if (route.query.videoId || route.query.caseId) {
                previewMode.value = route.query.videoId ? "video" : "case";
            }
            break;
    }

    if (route.query.videoId) {
        let { request } = await getVideoDetail(route.query.videoId as string);
        await loadData({ ...request, draftId: "" });
    } else {
        // 获取创作（草稿）详情
        let {
            projectContent: { promptMap, ...reset },
            type,
        } = await getProjectDraft(formParamIds.draftId);

        // type是裂变混剪，则设置为裂变混剪模式(兼容直接从链接打开没有传projectType的兜底)
        if (type === 1) {
            previewMode.value = "fission";
        }

        await loadData({
            ...reset,
            promptMap: {
                ...promptMap,
                videoType: (formParamIds.draftId === "0" && history.state.videoType) || promptMap.videoType,
            },
        });
    }

    // 如果有脚本，则切换到分镜
    if (scripts.value.length) {
        switchStage("composeTable");
    }
});

// 是否为开发环境
if (["development", "test"].includes(appStore.ENV_MODE)) {
    Object.assign(window, {
        formConfig,
        formData,
        promptMap,
        digitalHumanConfigs,
        chooseDigitalHumanConfigs,
        subtitles,
        roles,
        scripts,
        updateProjectDraft,
    });
}
</script>

<style lang="less" scoped>
@import "./common/css/index.less";
</style>
